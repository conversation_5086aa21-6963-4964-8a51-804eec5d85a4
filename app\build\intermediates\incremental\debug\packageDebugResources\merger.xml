<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res"><file name="ic_dawn_logo" path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\drawable\ic_dawn_logo.xml" qualifiers="" type="drawable"/><file path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\values\file_types.xml" qualifiers=""><string name="file_type_video">Video files for entertainment and media</string><string name="file_type_audio">Audio files for music and sound</string><string name="file_type_image">Image files for photos and graphics</string><string name="file_type_document">Text documents and PDFs</string><string name="file_type_presentation">Presentation slides and decks</string><string name="file_type_spreadsheet">Spreadsheets and data tables</string><string name="file_type_archive">Compressed archives and packages</string><string name="file_type_ebook">Electronic books and publications</string><string name="file_type_code">Source code and scripts</string><string name="file_type_font">Font files for typography</string><string name="file_type_database">Database files and data storage</string><string name="file_type_cad">CAD drawings and 3D models</string><string name="file_type_vector">Vector graphics and illustrations</string><string name="file_type_executable">Executable programs and installers</string><string name="file_type_apk">Android application packages</string><string name="supported_video_formats">MP4, AVI, MKV, MOV, WMV, FLV, WebM, 3GP, and more</string><string name="supported_audio_formats">MP3, WAV, FLAC, AAC, OGG, WMA, M4A, OPUS, and more</string><string name="supported_image_formats">JPG, PNG, GIF, WebP, SVG, TIFF, BMP, HEIC, and more</string><string name="supported_document_formats">PDF, DOC, DOCX, TXT, RTF, ODT, Pages, and more</string><string name="supported_presentation_formats">PPT, PPTX, ODP, KEY, PPS, and more</string><string name="supported_spreadsheet_formats">XLS, XLSX, ODS, Numbers, CSV, and more</string><string name="supported_archive_formats">ZIP, RAR, 7Z, TAR, GZ, BZ2, ISO, and more</string><string name="supported_ebook_formats">EPUB, MOBI, AZW, FB2, LIT, and more</string><string name="supported_code_formats">HTML, CSS, JS, Python, Java, C++, Kotlin, and more</string><string name="supported_font_formats">TTF, OTF, WOFF, WOFF2, EOT, and more</string><string name="supported_database_formats">SQLite, DB, MDB, ACCDB, DBF, and more</string><string name="supported_cad_formats">DWG, DXF, STEP, STL, OBJ, 3DS, and more</string></file><file path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Dawn</string><string name="paste_url_hint">Paste any file URL here...</string><string name="preview">Preview</string><string name="open_with">Open with</string><string name="share_file">Share file</string><string name="file_not_found">File not found</string><string name="dawn_tagline">Download anything, preview instantly</string><string name="download">Download</string><string name="downloading">Downloading...</string><string name="download_complete">Download Complete</string><string name="download_failed">Download Failed</string><string name="invalid_url">Invalid URL</string><string name="no_internet">No internet connection</string><string name="permission_required">Storage permission required</string><string name="download_history">Download History</string><string name="clear_history">Clear History</string><string name="pause">Pause</string><string name="resume">Resume</string><string name="cancel">Cancel</string><string name="retry">Retry</string><string name="file_size">File Size</string><string name="download_speed">Download Speed</string><string name="time_remaining">Time Remaining</string><string name="clipboard_detected">URL detected in clipboard</string><string name="use_clipboard_url">Use this URL?</string></file><file path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Downloader" parent="android:Theme.Holo.Light.NoActionBar">
        <item name="android:windowBackground">@android:color/white</item>
    </style></file><file path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.Downloader" parent="android:Theme.Holo.NoActionBar">
        <item name="android:windowBackground">@android:color/black</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_provider_paths" path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\xml\file_provider_paths.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\rv\Downloader\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\rv\Downloader\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\rv\Downloader\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>