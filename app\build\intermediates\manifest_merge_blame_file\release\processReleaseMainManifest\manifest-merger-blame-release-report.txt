1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.downloader.app"
4    android:versionCode="2"
5    android:versionName="2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Internet permission for downloading files -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Storage permissions for saving files -->
15    <uses-permission
15-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:12:9-35
21
22    <!-- For Android 13+ -->
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:15:5-76
23-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:15:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:16:5-75
24-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:16:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:17:5-75
25-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:17:22-72
26
27    <!-- Network state for checking connectivity -->
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
28-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:20:5-79
28-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:20:22-76
29
30    <!-- Foreground service for downloads -->
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:23:5-77
31-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:23:22-74
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
32-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:24:5-87
32-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:24:22-84
33
34    <!-- Notification permission for Android 13+ -->
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:27:5-77
35-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:27:22-74
36    <uses-permission android:name="android.permission.READ_CLIPBOARD" />
36-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:30:5-73
36-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:30:22-70
37    <uses-permission android:name="android.permission.WAKE_LOCK" />
37-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:5-68
37-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:22-65
38    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
38-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:5-81
38-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:22-78
39
40    <permission
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
41        android:name="com.downloader.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.downloader.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
45
46    <application
46-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:32:5-81:19
47        android:name="com.downloader.app.DawnApplication"
47-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:33:9-40
48        android:allowBackup="true"
48-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:34:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:35:9-65
51        android:extractNativeLibs="false"
52        android:fullBackupContent="@xml/backup_rules"
52-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:36:9-54
53        android:icon="@drawable/ic_dawn_logo"
53-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:37:9-46
54        android:label="@string/app_name"
54-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:38:9-41
55        android:networkSecurityConfig="@xml/network_security_config"
55-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:43:9-69
56        android:roundIcon="@drawable/ic_dawn_logo"
56-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:39:9-51
57        android:supportsRtl="true"
57-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:40:9-35
58        android:theme="@style/Theme.Downloader"
58-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:41:9-48
59        android:usesCleartextTraffic="true" >
59-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:42:9-44
60        <activity
60-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:46:9-61:20
61            android:name="com.downloader.app.MainActivity"
61-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:47:13-41
62            android:exported="true"
62-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:48:13-36
63            android:theme="@style/Theme.Downloader" >
63-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:49:13-52
64            <intent-filter>
64-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:50:13-53:29
65                <action android:name="android.intent.action.MAIN" />
65-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:51:17-69
65-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:51:25-66
66
67                <category android:name="android.intent.category.LAUNCHER" />
67-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:52:17-77
67-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:52:27-74
68            </intent-filter>
69
70            <!-- Handle shared URLs -->
71            <intent-filter>
71-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:56:13-60:29
72                <action android:name="android.intent.action.SEND" />
72-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:57:17-69
72-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:57:25-66
73
74                <category android:name="android.intent.category.DEFAULT" />
74-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:58:17-76
74-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:58:27-73
75
76                <data android:mimeType="text/plain" />
76-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:59:17-55
76-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:59:23-52
77            </intent-filter>
78        </activity>
79
80        <!-- Download Service -->
81        <service
81-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:64:9-68:56
82            android:name="com.downloader.app.service.DownloadService"
82-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:65:13-52
83            android:enabled="true"
83-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:66:13-35
84            android:exported="false"
84-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:67:13-37
85            android:foregroundServiceType="dataSync" />
85-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:68:13-53
86
87        <!-- File Provider for sharing files -->
88        <provider
89            android:name="androidx.core.content.FileProvider"
89-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:72:13-62
90            android:authorities="com.downloader.app.fileprovider"
90-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:73:13-64
91            android:exported="false"
91-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:74:13-37
92            android:grantUriPermissions="true" >
92-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:75:13-47
93            <meta-data
93-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:76:13-78:63
94                android:name="android.support.FILE_PROVIDER_PATHS"
94-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:77:17-67
95                android:resource="@xml/file_provider_paths" />
95-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:78:17-60
96        </provider>
97        <provider
97-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:29:9-37:20
98            android:name="androidx.startup.InitializationProvider"
98-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:30:13-67
99            android:authorities="com.downloader.app.androidx-startup"
99-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:31:13-68
100            android:exported="false" >
100-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:32:13-37
101            <meta-data
101-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:34:13-36:52
102                android:name="androidx.work.WorkManagerInitializer"
102-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:35:17-68
103                android:value="androidx.startup" />
103-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:36:17-49
104            <meta-data
104-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
105-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
106                android:value="androidx.startup" />
106-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.emoji2.text.EmojiCompatInitializer"
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
109                android:value="androidx.startup" />
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
112                android:value="androidx.startup" />
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
113        </provider>
114
115        <service
115-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:39:9-45:35
116            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
116-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:40:13-88
117            android:directBootAware="false"
117-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:41:13-44
118            android:enabled="@bool/enable_system_alarm_service_default"
118-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:42:13-72
119            android:exported="false" />
119-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:43:13-37
120        <service
120-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:46:9-52:35
121            android:name="androidx.work.impl.background.systemjob.SystemJobService"
121-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:47:13-84
122            android:directBootAware="false"
122-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:48:13-44
123            android:enabled="@bool/enable_system_job_service_default"
123-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:49:13-70
124            android:exported="true"
124-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:50:13-36
125            android:permission="android.permission.BIND_JOB_SERVICE" />
125-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:51:13-69
126        <service
126-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:53:9-59:35
127            android:name="androidx.work.impl.foreground.SystemForegroundService"
127-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:54:13-81
128            android:directBootAware="false"
128-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:55:13-44
129            android:enabled="@bool/enable_system_foreground_service_default"
129-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:56:13-77
130            android:exported="false" />
130-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:57:13-37
131
132        <receiver
132-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:61:9-66:35
133            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
133-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:62:13-88
134            android:directBootAware="false"
134-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:63:13-44
135            android:enabled="true"
135-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:64:13-35
136            android:exported="false" />
136-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:65:13-37
137        <receiver
137-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:67:9-77:20
138            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
138-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:68:13-106
139            android:directBootAware="false"
139-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:69:13-44
140            android:enabled="false"
140-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:70:13-36
141            android:exported="false" >
141-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:71:13-37
142            <intent-filter>
142-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:73:13-76:29
143                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
143-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:17-87
143-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:25-84
144                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
144-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:17-90
144-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:25-87
145            </intent-filter>
146        </receiver>
147        <receiver
147-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:78:9-88:20
148            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
148-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:79:13-104
149            android:directBootAware="false"
149-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:80:13-44
150            android:enabled="false"
150-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:81:13-36
151            android:exported="false" >
151-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:82:13-37
152            <intent-filter>
152-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:84:13-87:29
153                <action android:name="android.intent.action.BATTERY_OKAY" />
153-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:17-77
153-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:25-74
154                <action android:name="android.intent.action.BATTERY_LOW" />
154-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:17-76
154-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:25-73
155            </intent-filter>
156        </receiver>
157        <receiver
157-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:89:9-99:20
158            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
158-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:90:13-104
159            android:directBootAware="false"
159-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:91:13-44
160            android:enabled="false"
160-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:92:13-36
161            android:exported="false" >
161-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:93:13-37
162            <intent-filter>
162-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:95:13-98:29
163                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
163-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:17-83
163-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:25-80
164                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
164-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:17-82
164-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:25-79
165            </intent-filter>
166        </receiver>
167        <receiver
167-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:100:9-109:20
168            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
168-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:101:13-103
169            android:directBootAware="false"
169-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:102:13-44
170            android:enabled="false"
170-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:103:13-36
171            android:exported="false" >
171-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:104:13-37
172            <intent-filter>
172-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:106:13-108:29
173                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
173-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:17-79
173-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:25-76
174            </intent-filter>
175        </receiver>
176        <receiver
176-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:110:9-121:20
177            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
177-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:111:13-88
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:112:13-44
179            android:enabled="false"
179-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:113:13-36
180            android:exported="false" >
180-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:114:13-37
181            <intent-filter>
181-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:116:13-120:29
182                <action android:name="android.intent.action.BOOT_COMPLETED" />
182-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:117:17-79
182-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:117:25-76
183                <action android:name="android.intent.action.TIME_SET" />
183-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:17-73
183-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:25-70
184                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
184-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:17-81
184-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:25-78
185            </intent-filter>
186        </receiver>
187        <receiver
187-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:122:9-131:20
188            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
188-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:123:13-99
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:124:13-44
190            android:enabled="@bool/enable_system_alarm_service_default"
190-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:125:13-72
191            android:exported="false" >
191-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:126:13-37
192            <intent-filter>
192-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:128:13-130:29
193                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
193-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:17-98
193-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:25-95
194            </intent-filter>
195        </receiver>
196        <receiver
196-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:132:9-142:20
197            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
197-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:133:13-78
198            android:directBootAware="false"
198-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:134:13-44
199            android:enabled="true"
199-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:135:13-35
200            android:exported="true"
200-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:136:13-36
201            android:permission="android.permission.DUMP" >
201-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:137:13-57
202            <intent-filter>
202-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:139:13-141:29
203                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
203-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:17-88
203-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:25-85
204            </intent-filter>
205        </receiver>
206
207        <service
207-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
208            android:name="androidx.room.MultiInstanceInvalidationService"
208-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
209            android:directBootAware="true"
209-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
210            android:exported="false" />
210-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
211
212        <receiver
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
213            android:name="androidx.profileinstaller.ProfileInstallReceiver"
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
214            android:directBootAware="false"
214-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
215            android:enabled="true"
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
216            android:exported="true"
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
217            android:permission="android.permission.DUMP" >
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
218            <intent-filter>
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
219                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
220            </intent-filter>
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
222                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
223            </intent-filter>
224            <intent-filter>
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
225                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
226            </intent-filter>
227            <intent-filter>
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
228                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
229            </intent-filter>
230        </receiver>
231    </application>
232
233</manifest>
