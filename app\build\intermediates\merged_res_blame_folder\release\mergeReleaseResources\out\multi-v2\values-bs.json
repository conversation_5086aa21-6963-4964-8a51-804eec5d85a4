{"logs": [{"outputFile": "com.downloader.app-mergeReleaseResources-72:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d62d9a540e552a1187e018192472b047\\transformed\\material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4711,4805,4896,5005,5093,5176,5273,5377,5470,5567,5655,5763,5860,5962,6100,6190,6298", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4706,4800,4891,5000,5088,5171,5268,5372,5465,5562,5650,5758,5855,5957,6095,6185,6293,6392"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8869,8988,9108,9229,9349,9448,9546,9661,9806,9926,10064,10149,10249,10342,10440,10557,10684,10789,10924,11058,11199,11369,11504,11627,11754,11882,11976,12074,12195,12323,12420,12523,12632,12771,12916,13025,13125,13210,13303,13398,13525,13619,13710,13819,13907,13990,14087,14191,14284,14381,14469,14577,14674,14776,14914,15004,15112", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "8983,9103,9224,9344,9443,9541,9656,9801,9921,10059,10144,10244,10337,10435,10552,10679,10784,10919,11053,11194,11364,11499,11622,11749,11877,11971,12069,12190,12318,12415,12518,12627,12766,12911,13020,13120,13205,13298,13393,13520,13614,13705,13814,13902,13985,14082,14186,14279,14376,14464,14572,14669,14771,14909,14999,15107,15206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74d3d7c2d24a7100d6b0d87b145b1bf3\\transformed\\core-1.15.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "48,49,50,51,52,53,54,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3615,3713,3815,3913,4017,4121,4223,15948", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3708,3810,3908,4012,4116,4218,4335,16044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1328e28f1361bb606dde64f609a081c\\transformed\\media3-ui-1.2.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,835,921,1008,1096,1194,1301,1371,1438,1534,1626,1691,1764,1827,1895,2009,2125,2241,2321,2405,2476,2547,2648,2750,2822,2892,2945,3003,3051,3112,3184,3251,3315,3386,3450,3509,3574,3639,3710,3762,3829,3910,3991", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "288,566,830,916,1003,1091,1189,1296,1366,1433,1529,1621,1686,1759,1822,1890,2004,2120,2236,2316,2400,2471,2542,2643,2745,2817,2887,2940,2998,3046,3107,3179,3246,3310,3381,3445,3504,3569,3634,3705,3757,3824,3905,3986,4042"}, "to": {"startLines": "2,11,16,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,621,4808,4894,4981,5069,5167,5274,5344,5411,5507,5599,5664,5737,5800,5868,5982,6098,6214,6294,6378,6449,6520,6621,6723,6795,7545,7598,7656,7704,7765,7837,7904,7968,8039,8103,8162,8227,8292,8363,8415,8482,8563,8644", "endLines": "10,15,20,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "338,616,880,4889,4976,5064,5162,5269,5339,5406,5502,5594,5659,5732,5795,5863,5977,6093,6209,6289,6373,6444,6515,6616,6718,6790,6860,7593,7651,7699,7760,7832,7899,7963,8034,8098,8157,8222,8287,8358,8410,8477,8558,8639,8695"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\478b3be060432db7073f96b7c2278ef6\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,1000,1086,1159,1236,1315,1392,1472,1542", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,995,1081,1154,1231,1310,1387,1467,1537,1655"}, "to": {"startLines": "55,56,57,58,59,111,112,170,171,172,173,175,176,177,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4340,4441,4529,4623,4722,8700,8777,15211,15303,15388,15469,15642,15715,15792,15871,16049,16129,16199", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "4436,4524,4618,4717,4803,8772,8864,15298,15383,15464,15550,15710,15787,15866,15943,16124,16194,16312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f05148d81958cc6d8e30b6d34a1ab13\\transformed\\media3-exoplayer-1.2.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,581,662", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "125,186,251,324,403,476,576,657,730"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6865,6940,7001,7066,7139,7218,7291,7391,7472", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "6935,6996,7061,7134,7213,7286,7386,7467,7540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7aa4dd35acc84f087f7df6becf4b1038\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "16317,16407", "endColumns": "89,91", "endOffsets": "16402,16494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28c5dc97a63a31061752728abbdc10f0\\transformed\\appcompat-1.7.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2248,2353,2467,2570,2739,2835", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2243,2348,2462,2565,2734,2830,2917"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "885,1006,1103,1210,1296,1400,1522,1607,1689,1780,1873,1968,2062,2162,2255,2350,2445,2536,2627,2715,2818,2922,3028,3133,3247,3350,3519,15555", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,105,104,113,102,168,95,86", "endOffsets": "1001,1098,1205,1291,1395,1517,1602,1684,1775,1868,1963,2057,2157,2250,2345,2440,2531,2622,2710,2813,2917,3023,3128,3242,3345,3514,3610,15637"}}]}]}