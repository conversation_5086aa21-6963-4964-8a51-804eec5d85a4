package com.downloader.app.ui.viewmodel;

import android.content.Context;
import com.downloader.app.service.InstagramExtractorService;
import com.downloader.app.service.YouTubeExtractorService;
import com.downloader.app.utils.ClipboardHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DownloadViewModel_Factory implements Factory<DownloadViewModel> {
  private final Provider<ClipboardHelper> clipboardHelperProvider;

  private final Provider<YouTubeExtractorService> youTubeExtractorServiceProvider;

  private final Provider<InstagramExtractorService> instagramExtractorServiceProvider;

  private final Provider<Context> contextProvider;

  public DownloadViewModel_Factory(Provider<ClipboardHelper> clipboardHelperProvider,
      Provider<YouTubeExtractorService> youTubeExtractorServiceProvider,
      Provider<InstagramExtractorService> instagramExtractorServiceProvider,
      Provider<Context> contextProvider) {
    this.clipboardHelperProvider = clipboardHelperProvider;
    this.youTubeExtractorServiceProvider = youTubeExtractorServiceProvider;
    this.instagramExtractorServiceProvider = instagramExtractorServiceProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public DownloadViewModel get() {
    return newInstance(clipboardHelperProvider.get(), youTubeExtractorServiceProvider.get(), instagramExtractorServiceProvider.get(), contextProvider.get());
  }

  public static DownloadViewModel_Factory create(Provider<ClipboardHelper> clipboardHelperProvider,
      Provider<YouTubeExtractorService> youTubeExtractorServiceProvider,
      Provider<InstagramExtractorService> instagramExtractorServiceProvider,
      Provider<Context> contextProvider) {
    return new DownloadViewModel_Factory(clipboardHelperProvider, youTubeExtractorServiceProvider, instagramExtractorServiceProvider, contextProvider);
  }

  public static DownloadViewModel newInstance(ClipboardHelper clipboardHelper,
      YouTubeExtractorService youTubeExtractorService,
      InstagramExtractorService instagramExtractorService, Context context) {
    return new DownloadViewModel(clipboardHelper, youTubeExtractorService, instagramExtractorService, context);
  }
}
