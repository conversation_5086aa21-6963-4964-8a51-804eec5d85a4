  Context android.content  InstantTaskExecutorRule #androidx.arch.core.executor.testing  clearHistory androidx.lifecycle.ViewModel  clearMessage androidx.lifecycle.ViewModel  dismissClipboardDialog androidx.lifecycle.ViewModel  
startDownload androidx.lifecycle.ViewModel  	updateUrl androidx.lifecycle.ViewModel  TurbineTestContext app.cash.turbine  test app.cash.turbine  
assertThat #app.cash.turbine.TurbineTestContext  	awaitItem #app.cash.turbine.TurbineTestContext  
getASSERTThat #app.cash.turbine.TurbineTestContext  
getAssertThat #app.cash.turbine.TurbineTestContext  DownloadItem com.downloader.app.data.model  DownloadStatus com.downloader.app.data.model  PENDING ,com.downloader.app.data.model.DownloadStatus  ExperimentalCoroutinesApi com.downloader.app.service  InstagramExtractorService com.downloader.app.service  InstagramExtractorServiceTest com.downloader.app.service  InstantTaskExecutorRule com.downloader.app.service  OptIn com.downloader.app.service  StandardTestDispatcher com.downloader.app.service  YouTubeExtractorService com.downloader.app.service  YouTubeExtractorServiceTest com.downloader.app.service  
assertThat com.downloader.app.service  forEach com.downloader.app.service  instagramExtractorService com.downloader.app.service  listOf com.downloader.app.service  runTest com.downloader.app.service  youTubeExtractorService com.downloader.app.service  extractInstagramUrl 4com.downloader.app.service.InstagramExtractorService  After 8com.downloader.app.service.InstagramExtractorServiceTest  Before 8com.downloader.app.service.InstagramExtractorServiceTest  Context 8com.downloader.app.service.InstagramExtractorServiceTest  InstagramExtractorService 8com.downloader.app.service.InstagramExtractorServiceTest  InstantTaskExecutorRule 8com.downloader.app.service.InstagramExtractorServiceTest  Rule 8com.downloader.app.service.InstagramExtractorServiceTest  StandardTestDispatcher 8com.downloader.app.service.InstagramExtractorServiceTest  Test 8com.downloader.app.service.InstagramExtractorServiceTest  
assertThat 8com.downloader.app.service.InstagramExtractorServiceTest  
getASSERTThat 8com.downloader.app.service.InstagramExtractorServiceTest  
getAssertThat 8com.downloader.app.service.InstagramExtractorServiceTest  
getRUNTest 8com.downloader.app.service.InstagramExtractorServiceTest  
getRunTest 8com.downloader.app.service.InstagramExtractorServiceTest  instagramExtractorService 8com.downloader.app.service.InstagramExtractorServiceTest  runTest 8com.downloader.app.service.InstagramExtractorServiceTest  extractYouTubeUrl 2com.downloader.app.service.YouTubeExtractorService  After 6com.downloader.app.service.YouTubeExtractorServiceTest  Before 6com.downloader.app.service.YouTubeExtractorServiceTest  Context 6com.downloader.app.service.YouTubeExtractorServiceTest  InstantTaskExecutorRule 6com.downloader.app.service.YouTubeExtractorServiceTest  Rule 6com.downloader.app.service.YouTubeExtractorServiceTest  StandardTestDispatcher 6com.downloader.app.service.YouTubeExtractorServiceTest  Test 6com.downloader.app.service.YouTubeExtractorServiceTest  YouTubeExtractorService 6com.downloader.app.service.YouTubeExtractorServiceTest  
assertThat 6com.downloader.app.service.YouTubeExtractorServiceTest  
getASSERTThat 6com.downloader.app.service.YouTubeExtractorServiceTest  
getAssertThat 6com.downloader.app.service.YouTubeExtractorServiceTest  	getLISTOf 6com.downloader.app.service.YouTubeExtractorServiceTest  	getListOf 6com.downloader.app.service.YouTubeExtractorServiceTest  
getRUNTest 6com.downloader.app.service.YouTubeExtractorServiceTest  
getRunTest 6com.downloader.app.service.YouTubeExtractorServiceTest  listOf 6com.downloader.app.service.YouTubeExtractorServiceTest  runTest 6com.downloader.app.service.YouTubeExtractorServiceTest  youTubeExtractorService 6com.downloader.app.service.YouTubeExtractorServiceTest  Date com.downloader.app.ui.viewmodel  DownloadItem com.downloader.app.ui.viewmodel  DownloadStatus com.downloader.app.ui.viewmodel  DownloadUiState com.downloader.app.ui.viewmodel  DownloadViewModel com.downloader.app.ui.viewmodel  DownloadViewModelTest com.downloader.app.ui.viewmodel  	Exception com.downloader.app.ui.viewmodel  ExperimentalCoroutinesApi com.downloader.app.ui.viewmodel  InstantTaskExecutorRule com.downloader.app.ui.viewmodel  OptIn com.downloader.app.ui.viewmodel  Result com.downloader.app.ui.viewmodel  StandardTestDispatcher com.downloader.app.ui.viewmodel  UrlDetectionTest com.downloader.app.ui.viewmodel  
assertThat com.downloader.app.ui.viewmodel  coEvery com.downloader.app.ui.viewmodel  coVerify com.downloader.app.ui.viewmodel  let com.downloader.app.ui.viewmodel  runTest com.downloader.app.ui.viewmodel  test com.downloader.app.ui.viewmodel  testDispatcher com.downloader.app.ui.viewmodel  	viewModel com.downloader.app.ui.viewmodel  youTubeExtractorService com.downloader.app.ui.viewmodel  clipboardUrl /com.downloader.app.ui.viewmodel.DownloadUiState  copy /com.downloader.app.ui.viewmodel.DownloadUiState  
currentUrl /com.downloader.app.ui.viewmodel.DownloadUiState  errorMessage /com.downloader.app.ui.viewmodel.DownloadUiState  getLET /com.downloader.app.ui.viewmodel.DownloadUiState  getLet /com.downloader.app.ui.viewmodel.DownloadUiState  let /com.downloader.app.ui.viewmodel.DownloadUiState  showClipboardDialog /com.downloader.app.ui.viewmodel.DownloadUiState  successMessage /com.downloader.app.ui.viewmodel.DownloadUiState  clearHistory 1com.downloader.app.ui.viewmodel.DownloadViewModel  clearMessage 1com.downloader.app.ui.viewmodel.DownloadViewModel  dismissClipboardDialog 1com.downloader.app.ui.viewmodel.DownloadViewModel  	downloads 1com.downloader.app.ui.viewmodel.DownloadViewModel  
startDownload 1com.downloader.app.ui.viewmodel.DownloadViewModel  uiState 1com.downloader.app.ui.viewmodel.DownloadViewModel  	updateUrl 1com.downloader.app.ui.viewmodel.DownloadViewModel  After 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Before 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  ClipboardHelper 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Context 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Date 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  DownloadItem 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  DownloadStatus 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  DownloadViewModel 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  	Exception 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  InstantTaskExecutorRule 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Result 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Rule 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  StandardTestDispatcher 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Test 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  YouTubeExtractorService 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
assertThat 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  coEvery 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  coVerify 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getASSERTThat 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getAssertThat 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getCOEvery 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getCOVerify 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getCoEvery 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getCoVerify 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getLET 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getLet 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getRUNTest 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getRunTest 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getTEST 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getTest 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  let 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  runTest 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  test 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  testDispatcher 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  	viewModel 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  youTubeExtractorService 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  After 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Before 0com.downloader.app.ui.viewmodel.UrlDetectionTest  ClipboardHelper 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Context 0com.downloader.app.ui.viewmodel.UrlDetectionTest  DownloadViewModel 0com.downloader.app.ui.viewmodel.UrlDetectionTest  InstantTaskExecutorRule 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Rule 0com.downloader.app.ui.viewmodel.UrlDetectionTest  StandardTestDispatcher 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Test 0com.downloader.app.ui.viewmodel.UrlDetectionTest  YouTubeExtractorService 0com.downloader.app.ui.viewmodel.UrlDetectionTest  ClipboardHelper com.downloader.app.utils  
FileUtilsTest com.downloader.app.utils  Test &com.downloader.app.utils.FileUtilsTest  BooleanSubject com.google.common.truth  IterableSubject com.google.common.truth  
StringSubject com.google.common.truth  Subject com.google.common.truth  Truth com.google.common.truth  isFalse &com.google.common.truth.BooleanSubject  isTrue &com.google.common.truth.BooleanSubject  contains )com.google.common.truth.ComparableSubject  isEmpty )com.google.common.truth.ComparableSubject  	isEqualTo )com.google.common.truth.ComparableSubject  isEmpty 'com.google.common.truth.IterableSubject  contains %com.google.common.truth.StringSubject  isEmpty %com.google.common.truth.StringSubject  	isEqualTo %com.google.common.truth.StringSubject  contains com.google.common.truth.Subject  isEmpty com.google.common.truth.Subject  	isEqualTo com.google.common.truth.Subject  isFalse com.google.common.truth.Subject  	isNotNull com.google.common.truth.Subject  isTrue com.google.common.truth.Subject  
assertThat com.google.common.truth.Truth  Date io.mockk  DownloadItem io.mockk  DownloadStatus io.mockk  	Exception io.mockk  ExperimentalCoroutinesApi io.mockk  InstantTaskExecutorRule io.mockk  MockKAdditionalAnswerScope io.mockk  MockKMatcherScope io.mockk  MockKStubScope io.mockk  MockKVerificationScope io.mockk  Result io.mockk  StandardTestDispatcher io.mockk  
assertThat io.mockk  coEvery io.mockk  coVerify io.mockk  let io.mockk  mockk io.mockk  runTest io.mockk  test io.mockk  testDispatcher io.mockk  	viewModel io.mockk  youTubeExtractorService io.mockk  getYOUTubeExtractorService io.mockk.MockKMatcherScope  getYouTubeExtractorService io.mockk.MockKMatcherScope  youTubeExtractorService io.mockk.MockKMatcherScope  returns io.mockk.MockKStubScope  getYOUTubeExtractorService io.mockk.MockKVerificationScope  getYouTubeExtractorService io.mockk.MockKVerificationScope  youTubeExtractorService io.mockk.MockKVerificationScope  Date 	java.lang  DownloadItem 	java.lang  DownloadStatus 	java.lang  	Exception 	java.lang  ExperimentalCoroutinesApi 	java.lang  InstantTaskExecutorRule 	java.lang  Result 	java.lang  StandardTestDispatcher 	java.lang  
assertThat 	java.lang  coEvery 	java.lang  coVerify 	java.lang  forEach 	java.lang  instagramExtractorService 	java.lang  let 	java.lang  listOf 	java.lang  runTest 	java.lang  test 	java.lang  testDispatcher 	java.lang  	viewModel 	java.lang  youTubeExtractorService 	java.lang  Date 	java.util  Boolean kotlin  Date kotlin  DownloadItem kotlin  DownloadStatus kotlin  	Exception kotlin  ExperimentalCoroutinesApi kotlin  	Function1 kotlin  InstantTaskExecutorRule kotlin  OptIn kotlin  Result kotlin  StandardTestDispatcher kotlin  String kotlin  
assertThat kotlin  coEvery kotlin  coVerify kotlin  forEach kotlin  instagramExtractorService kotlin  let kotlin  listOf kotlin  runTest kotlin  test kotlin  testDispatcher kotlin  	viewModel kotlin  youTubeExtractorService kotlin  exceptionOrNull 
kotlin.Result  failure 
kotlin.Result  	isFailure 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  Date kotlin.annotation  DownloadItem kotlin.annotation  DownloadStatus kotlin.annotation  	Exception kotlin.annotation  ExperimentalCoroutinesApi kotlin.annotation  InstantTaskExecutorRule kotlin.annotation  Result kotlin.annotation  StandardTestDispatcher kotlin.annotation  
assertThat kotlin.annotation  coEvery kotlin.annotation  coVerify kotlin.annotation  forEach kotlin.annotation  instagramExtractorService kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  runTest kotlin.annotation  test kotlin.annotation  testDispatcher kotlin.annotation  	viewModel kotlin.annotation  youTubeExtractorService kotlin.annotation  Date kotlin.collections  DownloadItem kotlin.collections  DownloadStatus kotlin.collections  	Exception kotlin.collections  ExperimentalCoroutinesApi kotlin.collections  InstantTaskExecutorRule kotlin.collections  List kotlin.collections  Result kotlin.collections  StandardTestDispatcher kotlin.collections  
assertThat kotlin.collections  coEvery kotlin.collections  coVerify kotlin.collections  forEach kotlin.collections  instagramExtractorService kotlin.collections  let kotlin.collections  listOf kotlin.collections  runTest kotlin.collections  test kotlin.collections  testDispatcher kotlin.collections  	viewModel kotlin.collections  youTubeExtractorService kotlin.collections  Date kotlin.comparisons  DownloadItem kotlin.comparisons  DownloadStatus kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalCoroutinesApi kotlin.comparisons  InstantTaskExecutorRule kotlin.comparisons  Result kotlin.comparisons  StandardTestDispatcher kotlin.comparisons  
assertThat kotlin.comparisons  coEvery kotlin.comparisons  coVerify kotlin.comparisons  forEach kotlin.comparisons  instagramExtractorService kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  runTest kotlin.comparisons  test kotlin.comparisons  testDispatcher kotlin.comparisons  	viewModel kotlin.comparisons  youTubeExtractorService kotlin.comparisons  SuspendFunction1 kotlin.coroutines  advanceUntilIdle 1kotlin.coroutines.AbstractCoroutineContextElement  Date 	kotlin.io  DownloadItem 	kotlin.io  DownloadStatus 	kotlin.io  	Exception 	kotlin.io  ExperimentalCoroutinesApi 	kotlin.io  InstantTaskExecutorRule 	kotlin.io  Result 	kotlin.io  StandardTestDispatcher 	kotlin.io  
assertThat 	kotlin.io  coEvery 	kotlin.io  coVerify 	kotlin.io  forEach 	kotlin.io  instagramExtractorService 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  runTest 	kotlin.io  test 	kotlin.io  testDispatcher 	kotlin.io  	viewModel 	kotlin.io  youTubeExtractorService 	kotlin.io  Date 
kotlin.jvm  DownloadItem 
kotlin.jvm  DownloadStatus 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalCoroutinesApi 
kotlin.jvm  InstantTaskExecutorRule 
kotlin.jvm  Result 
kotlin.jvm  StandardTestDispatcher 
kotlin.jvm  
assertThat 
kotlin.jvm  coEvery 
kotlin.jvm  coVerify 
kotlin.jvm  forEach 
kotlin.jvm  instagramExtractorService 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  runTest 
kotlin.jvm  test 
kotlin.jvm  testDispatcher 
kotlin.jvm  	viewModel 
kotlin.jvm  youTubeExtractorService 
kotlin.jvm  Date 
kotlin.ranges  DownloadItem 
kotlin.ranges  DownloadStatus 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalCoroutinesApi 
kotlin.ranges  InstantTaskExecutorRule 
kotlin.ranges  Result 
kotlin.ranges  StandardTestDispatcher 
kotlin.ranges  
assertThat 
kotlin.ranges  coEvery 
kotlin.ranges  coVerify 
kotlin.ranges  forEach 
kotlin.ranges  instagramExtractorService 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  runTest 
kotlin.ranges  test 
kotlin.ranges  testDispatcher 
kotlin.ranges  	viewModel 
kotlin.ranges  youTubeExtractorService 
kotlin.ranges  KClass kotlin.reflect  Date kotlin.sequences  DownloadItem kotlin.sequences  DownloadStatus kotlin.sequences  	Exception kotlin.sequences  ExperimentalCoroutinesApi kotlin.sequences  InstantTaskExecutorRule kotlin.sequences  Result kotlin.sequences  StandardTestDispatcher kotlin.sequences  
assertThat kotlin.sequences  coEvery kotlin.sequences  coVerify kotlin.sequences  forEach kotlin.sequences  instagramExtractorService kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  runTest kotlin.sequences  test kotlin.sequences  testDispatcher kotlin.sequences  	viewModel kotlin.sequences  youTubeExtractorService kotlin.sequences  Date kotlin.text  DownloadItem kotlin.text  DownloadStatus kotlin.text  	Exception kotlin.text  ExperimentalCoroutinesApi kotlin.text  InstantTaskExecutorRule kotlin.text  Result kotlin.text  StandardTestDispatcher kotlin.text  
assertThat kotlin.text  coEvery kotlin.text  coVerify kotlin.text  forEach kotlin.text  instagramExtractorService kotlin.text  let kotlin.text  listOf kotlin.text  runTest kotlin.text  test kotlin.text  testDispatcher kotlin.text  	viewModel kotlin.text  youTubeExtractorService kotlin.text  Dispatchers kotlinx.coroutines  ExperimentalCoroutinesApi kotlinx.coroutines  flowOf kotlinx.coroutines.flow  getTEST (kotlinx.coroutines.flow.MutableStateFlow  getTest (kotlinx.coroutines.flow.MutableStateFlow  test (kotlinx.coroutines.flow.MutableStateFlow  getTEST !kotlinx.coroutines.flow.StateFlow  getTest !kotlinx.coroutines.flow.StateFlow  test !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  Date kotlinx.coroutines.test  DownloadItem kotlinx.coroutines.test  DownloadStatus kotlinx.coroutines.test  	Exception kotlinx.coroutines.test  ExperimentalCoroutinesApi kotlinx.coroutines.test  InstantTaskExecutorRule kotlinx.coroutines.test  Result kotlinx.coroutines.test  StandardTestDispatcher kotlinx.coroutines.test  TestDispatcher kotlinx.coroutines.test  	TestScope kotlinx.coroutines.test  
assertThat kotlinx.coroutines.test  coEvery kotlinx.coroutines.test  coVerify kotlinx.coroutines.test  forEach kotlinx.coroutines.test  instagramExtractorService kotlinx.coroutines.test  let kotlinx.coroutines.test  listOf kotlinx.coroutines.test  	resetMain kotlinx.coroutines.test  runTest kotlinx.coroutines.test  setMain kotlinx.coroutines.test  test kotlinx.coroutines.test  testDispatcher kotlinx.coroutines.test  	viewModel kotlinx.coroutines.test  youTubeExtractorService kotlinx.coroutines.test  advanceUntilIdle .kotlinx.coroutines.test.TestCoroutineScheduler  	scheduler &kotlinx.coroutines.test.TestDispatcher  Date !kotlinx.coroutines.test.TestScope  DownloadItem !kotlinx.coroutines.test.TestScope  DownloadStatus !kotlinx.coroutines.test.TestScope  	Exception !kotlinx.coroutines.test.TestScope  Result !kotlinx.coroutines.test.TestScope  
assertThat !kotlinx.coroutines.test.TestScope  coEvery !kotlinx.coroutines.test.TestScope  coVerify !kotlinx.coroutines.test.TestScope  
getASSERTThat !kotlinx.coroutines.test.TestScope  
getAssertThat !kotlinx.coroutines.test.TestScope  
getCOEvery !kotlinx.coroutines.test.TestScope  getCOVerify !kotlinx.coroutines.test.TestScope  
getCoEvery !kotlinx.coroutines.test.TestScope  getCoVerify !kotlinx.coroutines.test.TestScope  getINSTAGRAMExtractorService !kotlinx.coroutines.test.TestScope  getInstagramExtractorService !kotlinx.coroutines.test.TestScope  getLET !kotlinx.coroutines.test.TestScope  	getLISTOf !kotlinx.coroutines.test.TestScope  getLet !kotlinx.coroutines.test.TestScope  	getListOf !kotlinx.coroutines.test.TestScope  getTEST !kotlinx.coroutines.test.TestScope  getTESTDispatcher !kotlinx.coroutines.test.TestScope  getTest !kotlinx.coroutines.test.TestScope  getTestDispatcher !kotlinx.coroutines.test.TestScope  getVIEWModel !kotlinx.coroutines.test.TestScope  getViewModel !kotlinx.coroutines.test.TestScope  getYOUTubeExtractorService !kotlinx.coroutines.test.TestScope  getYouTubeExtractorService !kotlinx.coroutines.test.TestScope  instagramExtractorService !kotlinx.coroutines.test.TestScope  let !kotlinx.coroutines.test.TestScope  listOf !kotlinx.coroutines.test.TestScope  test !kotlinx.coroutines.test.TestScope  testDispatcher !kotlinx.coroutines.test.TestScope  	viewModel !kotlinx.coroutines.test.TestScope  youTubeExtractorService !kotlinx.coroutines.test.TestScope  After 	org.junit  Before 	org.junit  Rule 	org.junit  Test 	org.junit  InstagramExtractorService 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  InstagramExtractorService 0com.downloader.app.ui.viewmodel.UrlDetectionTest                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    