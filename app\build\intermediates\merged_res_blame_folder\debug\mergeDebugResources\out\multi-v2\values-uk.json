{"logs": [{"outputFile": "com.downloader.app-mergeDebugResources-76:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\478b3be060432db7073f96b7c2278ef6\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,995,1083,1155,1231,1308,1385,1465,1535", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,75,76,76,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,990,1078,1150,1226,1303,1380,1460,1530,1653"}, "to": {"startLines": "57,58,59,60,61,113,114,172,173,174,175,177,178,179,180,182,183,184", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4462,4555,4639,4741,4842,8880,8962,15351,15439,15521,15606,15776,15848,15924,16001,16179,16259,16329", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,75,76,76,79,69,122", "endOffsets": "4550,4634,4736,4837,4921,8957,9046,15434,15516,15601,15689,15843,15919,15996,16073,16254,16324,16447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28c5dc97a63a31061752728abbdc10f0\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,176", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1006,1115,1217,1325,1411,1516,1634,1717,1799,1890,1983,2078,2172,2272,2365,2460,2555,2646,2737,2836,2942,3048,3146,3253,3360,3465,3635,15694", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "1110,1212,1320,1406,1511,1629,1712,1794,1885,1978,2073,2167,2267,2360,2455,2550,2641,2732,2831,2937,3043,3141,3248,3355,3460,3630,3730,15771"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7aa4dd35acc84f087f7df6becf4b1038\\transformed\\foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,99", "endOffsets": "147,247"}, "to": {"startLines": "185,186", "startColumns": "4,4", "startOffsets": "16452,16549", "endColumns": "96,99", "endOffsets": "16544,16644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f05148d81958cc6d8e30b6d34a1ab13\\transformed\\media3-exoplayer-1.2.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "86,87,88,89,90,91,92,93,94", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7044,7118,7183,7251,7322,7402,7475,7568,7657", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "7113,7178,7246,7317,7397,7470,7563,7652,7727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1328e28f1361bb606dde64f609a081c\\transformed\\media3-ui-1.2.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,956,1040,1122,1205,1305,1404,1489,1552,1650,1749,1820,1889,1955,2023,2149,2274,2411,2488,2570,2645,2733,2828,2921,2989,3074,3127,3187,3235,3296,3363,3431,3495,3562,3627,3687,3753,3818,3884,3936,3997,4082,4167", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,64,65,51,60,84,84,54", "endOffsets": "282,621,951,1035,1117,1200,1300,1399,1484,1547,1645,1744,1815,1884,1950,2018,2144,2269,2406,2483,2565,2640,2728,2823,2916,2984,3069,3122,3182,3230,3291,3358,3426,3490,3557,3622,3682,3748,3813,3879,3931,3992,4077,4162,4217"}, "to": {"startLines": "2,11,17,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,676,4926,5010,5092,5175,5275,5374,5459,5522,5620,5719,5790,5859,5925,5993,6119,6244,6381,6458,6540,6615,6703,6798,6891,6959,7732,7785,7845,7893,7954,8021,8089,8153,8220,8285,8345,8411,8476,8542,8594,8655,8740,8825", "endLines": "10,16,22,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,64,65,51,60,84,84,54", "endOffsets": "332,671,1001,5005,5087,5170,5270,5369,5454,5517,5615,5714,5785,5854,5920,5988,6114,6239,6376,6453,6535,6610,6698,6793,6886,6954,7039,7780,7840,7888,7949,8016,8084,8148,8215,8280,8340,8406,8471,8537,8589,8650,8735,8820,8875"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d62d9a540e552a1187e018192472b047\\transformed\\material3-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,289,407,525,624,719,831,969,1085,1232,1316,1416,1509,1605,1721,1845,1950,2091,2228,2363,2552,2679,2803,2932,3053,3147,3248,3374,3504,3602,3707,3816,3961,4112,4220,4320,4395,4490,4586,4705,4791,4878,4977,5057,5143,5242,5346,5441,5541,5630,5737,5833,5936,6054,6134,6249", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "168,284,402,520,619,714,826,964,1080,1227,1311,1411,1504,1600,1716,1840,1945,2086,2223,2358,2547,2674,2798,2927,3048,3142,3243,3369,3499,3597,3702,3811,3956,4107,4215,4315,4390,4485,4581,4700,4786,4873,4972,5052,5138,5237,5341,5436,5536,5625,5732,5828,5931,6049,6129,6244,6350"}, "to": {"startLines": "115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "9051,9169,9285,9403,9521,9620,9715,9827,9965,10081,10228,10312,10412,10505,10601,10717,10841,10946,11087,11224,11359,11548,11675,11799,11928,12049,12143,12244,12370,12500,12598,12703,12812,12957,13108,13216,13316,13391,13486,13582,13701,13787,13874,13973,14053,14139,14238,14342,14437,14537,14626,14733,14829,14932,15050,15130,15245", "endColumns": "117,115,117,117,98,94,111,137,115,146,83,99,92,95,115,123,104,140,136,134,188,126,123,128,120,93,100,125,129,97,104,108,144,150,107,99,74,94,95,118,85,86,98,79,85,98,103,94,99,88,106,95,102,117,79,114,105", "endOffsets": "9164,9280,9398,9516,9615,9710,9822,9960,10076,10223,10307,10407,10500,10596,10712,10836,10941,11082,11219,11354,11543,11670,11794,11923,12044,12138,12239,12365,12495,12593,12698,12807,12952,13103,13211,13311,13386,13481,13577,13696,13782,13869,13968,14048,14134,14233,14337,14432,14532,14621,14728,14824,14927,15045,15125,15240,15346"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74d3d7c2d24a7100d6b0d87b145b1bf3\\transformed\\core-1.15.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "50,51,52,53,54,55,56,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3735,3835,3937,4038,4139,4244,4349,16078", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3830,3932,4033,4134,4239,4344,4457,16174"}}]}]}