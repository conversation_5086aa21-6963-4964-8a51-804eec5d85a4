@echo off
echo ========================================
echo Universal Downloader - Project Verification
echo ========================================
echo.

echo [1/5] Checking project structure...
if exist "app\src\main\java\com\downloader\app\MainActivity.kt" (
    echo ✓ MainActivity found
) else (
    echo ✗ MainActivity missing
)

if exist "app\src\main\AndroidManifest.xml" (
    echo ✓ AndroidManifest found
) else (
    echo ✗ AndroidManifest missing
)

if exist "gradle\libs.versions.toml" (
    echo ✓ Version catalog found
) else (
    echo ✗ Version catalog missing
)

echo.
echo [2/5] Checking Gradle configuration...
call gradlew tasks --quiet >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Gradle configuration valid
) else (
    echo ✗ Gradle configuration has issues
)

echo.
echo [3/5] Testing Gradle build...
call gradlew assembleDebug --quiet
if %errorlevel% equ 0 (
    echo ✓ Debug build successful
) else (
    echo ✗ Debug build failed
)

echo.
echo [4/5] Checking output APK...
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✓ APK generated successfully
    for %%I in ("app\build\outputs\apk\debug\app-debug.apk") do echo   Size: %%~zI bytes
) else (
    echo ✗ APK not found
)

echo.
echo [5/5] Project verification complete!
echo.
echo ========================================
echo Summary:
echo ✓ Universal Downloader Android App
echo ✓ Kotlin + Jetpack Compose + Material 3
echo ✓ MVVM Architecture with Hilt DI
echo ✓ Room Database + OkHttp Downloads
echo ✓ Background Service + Notifications
echo ✓ Clipboard Monitoring + File Management
echo ========================================
echo.
echo Ready for Android Studio import!
echo Location: %CD%
echo.
pause