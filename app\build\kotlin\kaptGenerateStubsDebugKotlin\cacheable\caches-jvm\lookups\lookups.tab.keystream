  Manifest android  Activity android.app  Application android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  ActivityResultContracts android.app.Activity  Bundle android.app.Activity  DownloadViewModel android.app.Activity  Intent android.app.Activity  all android.app.Activity  androidx android.app.Activity  getValue android.app.Activity  provideDelegate android.app.Activity  registerForActivityResult android.app.Activity  
viewModels android.app.Activity  ConcurrentHashMap android.app.Service  CoroutineScope android.app.Service  Dispatchers android.app.Service  DownloadItem android.app.Service  IBinder android.app.Service  Inject android.app.Service  Int android.app.Service  Intent android.app.Service  Job android.app.Service  Long android.app.Service  NotificationHelper android.app.Service  OkHttpClient android.app.Service  
SupervisorJob android.app.Service  java android.app.Service  ClipboardManager android.content  Context android.content  DialogInterface android.content  Intent android.content  ActivityResultContracts android.content.Context  Bundle android.content.Context  CLIPBOARD_SERVICE android.content.Context  ConcurrentHashMap android.content.Context  CoroutineScope android.content.Context  Dispatchers android.content.Context  DownloadItem android.content.Context  DownloadViewModel android.content.Context  IBinder android.content.Context  Inject android.content.Context  Int android.content.Context  Intent android.content.Context  Job android.content.Context  Long android.content.Context  NotificationHelper android.content.Context  OkHttpClient android.content.Context  
SupervisorJob android.content.Context  all android.content.Context  androidx android.content.Context  getSystemService android.content.Context  getValue android.content.Context  java android.content.Context  provideDelegate android.content.Context  registerForActivityResult android.content.Context  
viewModels android.content.Context  ActivityResultContracts android.content.ContextWrapper  Bundle android.content.ContextWrapper  ConcurrentHashMap android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  DownloadItem android.content.ContextWrapper  DownloadViewModel android.content.ContextWrapper  IBinder android.content.ContextWrapper  Inject android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Job android.content.ContextWrapper  Long android.content.ContextWrapper  NotificationHelper android.content.ContextWrapper  OkHttpClient android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  all android.content.ContextWrapper  androidx android.content.ContextWrapper  getValue android.content.ContextWrapper  java android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  
viewModels android.content.ContextWrapper  dismiss android.content.DialogInterface  <SAM-CONSTRUCTOR> /android.content.DialogInterface.OnClickListener  PackageManager android.content.pm  Uri android.net  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  Log android.util  SparseArray android.util  ActivityResultContracts  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  DownloadViewModel  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  all  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  MimeTypeMap android.webkit  ComponentActivity androidx.activity  
viewModels androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  DownloadViewModel #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  all #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  ActivityResultContracts -androidx.activity.ComponentActivity.Companion  all -androidx.activity.ComponentActivity.Companion  androidx -androidx.activity.ComponentActivity.Companion  getALL -androidx.activity.ComponentActivity.Companion  getANDROIDX -androidx.activity.ComponentActivity.Companion  getAll -androidx.activity.ComponentActivity.Companion  getAndroidx -androidx.activity.ComponentActivity.Companion  getGETValue -androidx.activity.ComponentActivity.Companion  getGetValue -androidx.activity.ComponentActivity.Companion  getPROVIDEDelegate -androidx.activity.ComponentActivity.Companion  getProvideDelegate -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  
viewModels -androidx.activity.ComponentActivity.Companion  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  invoke ^androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions.Companion  AlertDialog androidx.appcompat.app  Builder "androidx.appcompat.app.AlertDialog  
setMessage *androidx.appcompat.app.AlertDialog.Builder  setPositiveButton *androidx.appcompat.app.AlertDialog.Builder  setTitle *androidx.appcompat.app.AlertDialog.Builder  show *androidx.appcompat.app.AlertDialog.Builder  animateContentSize androidx.compose.animation  animateFloatAsState androidx.compose.animation.core  Image androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Arrangement "androidx.compose.foundation.layout  
AttachFile "androidx.compose.foundation.layout  Book "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Code "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Description "androidx.compose.foundation.layout  DownloadStatus "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  Folder "androidx.compose.foundation.layout  GridOn "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  	MusicNote "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  	Slideshow "androidx.compose.foundation.layout  Storage "androidx.compose.foundation.layout  
TextFormat "androidx.compose.foundation.layout  ViewInAr "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  Launch 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  
AttachFile ,androidx.compose.material.icons.Icons.Filled  Book ,androidx.compose.material.icons.Icons.Filled  Brush ,androidx.compose.material.icons.Icons.Filled  Build ,androidx.compose.material.icons.Icons.Filled  Code ,androidx.compose.material.icons.Icons.Filled  Description ,androidx.compose.material.icons.Icons.Filled  Folder ,androidx.compose.material.icons.Icons.Filled  GridOn ,androidx.compose.material.icons.Icons.Filled  Image ,androidx.compose.material.icons.Icons.Filled  	MusicNote ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  	Slideshow ,androidx.compose.material.icons.Icons.Filled  Storage ,androidx.compose.material.icons.Icons.Filled  
TextFormat ,androidx.compose.material.icons.Icons.Filled  ViewInAr ,androidx.compose.material.icons.Icons.Filled  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  Launch 3androidx.compose.material.icons.automirrored.filled  	OpenInNew 3androidx.compose.material.icons.automirrored.filled  
AttachFile &androidx.compose.material.icons.filled  Book &androidx.compose.material.icons.filled  Brush &androidx.compose.material.icons.filled  Build &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  Close &androidx.compose.material.icons.filled  Code &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Description &androidx.compose.material.icons.filled  Download &androidx.compose.material.icons.filled  DownloadStatus &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Folder &androidx.compose.material.icons.filled  GridOn &androidx.compose.material.icons.filled  Image &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  Link &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  	MusicNote &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  	Slideshow &androidx.compose.material.icons.filled  Storage &androidx.compose.material.icons.filled  
TextFormat &androidx.compose.material.icons.filled  ViewInAr &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  
AttachFile androidx.compose.material3  Book androidx.compose.material3  Brush androidx.compose.material3  Build androidx.compose.material3  Button androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Code androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  Description androidx.compose.material3  DownloadStatus androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  Folder androidx.compose.material3  GridOn androidx.compose.material3  Image androidx.compose.material3  LinearProgressIndicator androidx.compose.material3  
MaterialTheme androidx.compose.material3  	MusicNote androidx.compose.material3  	PlayArrow androidx.compose.material3  Scaffold androidx.compose.material3  Settings androidx.compose.material3  	Slideshow androidx.compose.material3  Storage androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  	TextField androidx.compose.material3  
TextFormat androidx.compose.material3  
Typography androidx.compose.material3  ViewInAr androidx.compose.material3  androidx androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  error &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  outline &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  tertiary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  ActivityResultContracts androidx.compose.runtime  
AttachFile androidx.compose.runtime  Book androidx.compose.runtime  Brush androidx.compose.runtime  Build androidx.compose.runtime  Code androidx.compose.runtime  
Composable androidx.compose.runtime  Description androidx.compose.runtime  DownloadStatus androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  Folder androidx.compose.runtime  GridOn androidx.compose.runtime  Image androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  	MusicNote androidx.compose.runtime  	PlayArrow androidx.compose.runtime  Settings androidx.compose.runtime  
SideEffect androidx.compose.runtime  	Slideshow androidx.compose.runtime  Storage androidx.compose.runtime  
TextFormat androidx.compose.runtime  ViewInAr androidx.compose.runtime  all androidx.compose.runtime  androidx androidx.compose.runtime  getValue androidx.compose.runtime  provideDelegate androidx.compose.runtime  
viewModels androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Color androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextDecoration androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  ActivityResultContracts #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  DownloadViewModel #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  all #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  FileProvider androidx.core.content  WindowCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  ApplicationContext androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  ClipboardHelper androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  DownloadItem androidx.lifecycle.ViewModel  DownloadUiState androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  InstagramExtractorService androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  YouTubeExtractorService androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  collectAsStateWithLifecycle androidx.lifecycle.compose  	MediaItem androidx.media3.common  	ExoPlayer androidx.media3.exoplayer  
PlayerView androidx.media3.ui  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Delete 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  Query 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  	VideoMeta at.huber.youtubeExtractor  YouTubeExtractor at.huber.youtubeExtractor  YtFile at.huber.youtubeExtractor  
AsyncImage coil.compose  ActivityResultContracts com.downloader.app  DawnApplication com.downloader.app  MainActivity com.downloader.app  all com.downloader.app  androidx com.downloader.app  getValue com.downloader.app  provideDelegate com.downloader.app  
viewModels com.downloader.app  ActivityResultContracts com.downloader.app.MainActivity  Bundle com.downloader.app.MainActivity  DownloadViewModel com.downloader.app.MainActivity  Intent com.downloader.app.MainActivity  all com.downloader.app.MainActivity  androidx com.downloader.app.MainActivity  getALL com.downloader.app.MainActivity  getANDROIDX com.downloader.app.MainActivity  getAll com.downloader.app.MainActivity  getAndroidx com.downloader.app.MainActivity  getGETValue com.downloader.app.MainActivity  getGetValue com.downloader.app.MainActivity  getPROVIDEDelegate com.downloader.app.MainActivity  getProvideDelegate com.downloader.app.MainActivity  
getVIEWModels com.downloader.app.MainActivity  getValue com.downloader.app.MainActivity  
getViewModels com.downloader.app.MainActivity  provideDelegate com.downloader.app.MainActivity  registerForActivityResult com.downloader.app.MainActivity  
viewModels com.downloader.app.MainActivity  Dao  com.downloader.app.data.database  Delete  com.downloader.app.data.database  DownloadDao  com.downloader.app.data.database  Insert  com.downloader.app.data.database  Int  com.downloader.app.data.database  List  com.downloader.app.data.database  Long  com.downloader.app.data.database  OnConflictStrategy  com.downloader.app.data.database  Query  com.downloader.app.data.database  String  com.downloader.app.data.database  Update  com.downloader.app.data.database  Delete ,com.downloader.app.data.database.DownloadDao  DownloadItem ,com.downloader.app.data.database.DownloadDao  DownloadStatus ,com.downloader.app.data.database.DownloadDao  Flow ,com.downloader.app.data.database.DownloadDao  Insert ,com.downloader.app.data.database.DownloadDao  Int ,com.downloader.app.data.database.DownloadDao  List ,com.downloader.app.data.database.DownloadDao  Long ,com.downloader.app.data.database.DownloadDao  OnConflictStrategy ,com.downloader.app.data.database.DownloadDao  Query ,com.downloader.app.data.database.DownloadDao  String ,com.downloader.app.data.database.DownloadDao  Update ,com.downloader.app.data.database.DownloadDao  clearAllDownloads ,com.downloader.app.data.database.DownloadDao  deleteDownload ,com.downloader.app.data.database.DownloadDao  deleteDownloadsByStatus ,com.downloader.app.data.database.DownloadDao  updateDownload ,com.downloader.app.data.database.DownloadDao  updateDownloadError ,com.downloader.app.data.database.DownloadDao  updateDownloadProgress ,com.downloader.app.data.database.DownloadDao  updateDownloadStatus ,com.downloader.app.data.database.DownloadDao  DownloadItem com.downloader.app.data.model  DownloadStatus com.downloader.app.data.model  FileType com.downloader.app.data.model  Int com.downloader.app.data.model  Long com.downloader.app.data.model  String com.downloader.app.data.model  Date *com.downloader.app.data.model.DownloadItem  DownloadStatus *com.downloader.app.data.model.DownloadItem  Int *com.downloader.app.data.model.DownloadItem  Long *com.downloader.app.data.model.DownloadItem  String *com.downloader.app.data.model.DownloadItem  	CANCELLED ,com.downloader.app.data.model.DownloadStatus  	COMPLETED ,com.downloader.app.data.model.DownloadStatus  DOWNLOADING ,com.downloader.app.data.model.DownloadStatus  FAILED ,com.downloader.app.data.model.DownloadStatus  PAUSED ,com.downloader.app.data.model.DownloadStatus  PENDING ,com.downloader.app.data.model.DownloadStatus  APK &com.downloader.app.data.model.FileType  ARCHIVE &com.downloader.app.data.model.FileType  AUDIO &com.downloader.app.data.model.FileType  CAD &com.downloader.app.data.model.FileType  CODE &com.downloader.app.data.model.FileType  DATABASE &com.downloader.app.data.model.FileType  DOCUMENT &com.downloader.app.data.model.FileType  EBOOK &com.downloader.app.data.model.FileType  
EXECUTABLE &com.downloader.app.data.model.FileType  FONT &com.downloader.app.data.model.FileType  FileType &com.downloader.app.data.model.FileType  IMAGE &com.downloader.app.data.model.FileType  OTHER &com.downloader.app.data.model.FileType  PRESENTATION &com.downloader.app.data.model.FileType  SPREADSHEET &com.downloader.app.data.model.FileType  String &com.downloader.app.data.model.FileType  THREED_MODEL &com.downloader.app.data.model.FileType  VECTOR &com.downloader.app.data.model.FileType  VIDEO &com.downloader.app.data.model.FileType  APK 0com.downloader.app.data.model.FileType.Companion  ARCHIVE 0com.downloader.app.data.model.FileType.Companion  AUDIO 0com.downloader.app.data.model.FileType.Companion  CAD 0com.downloader.app.data.model.FileType.Companion  CODE 0com.downloader.app.data.model.FileType.Companion  DATABASE 0com.downloader.app.data.model.FileType.Companion  DOCUMENT 0com.downloader.app.data.model.FileType.Companion  EBOOK 0com.downloader.app.data.model.FileType.Companion  
EXECUTABLE 0com.downloader.app.data.model.FileType.Companion  FONT 0com.downloader.app.data.model.FileType.Companion  FileType 0com.downloader.app.data.model.FileType.Companion  IMAGE 0com.downloader.app.data.model.FileType.Companion  OTHER 0com.downloader.app.data.model.FileType.Companion  PRESENTATION 0com.downloader.app.data.model.FileType.Companion  SPREADSHEET 0com.downloader.app.data.model.FileType.Companion  String 0com.downloader.app.data.model.FileType.Companion  THREED_MODEL 0com.downloader.app.data.model.FileType.Companion  VECTOR 0com.downloader.app.data.model.FileType.Companion  VIDEO 0com.downloader.app.data.model.FileType.Companion  DownloadRepository "com.downloader.app.data.repository  Int "com.downloader.app.data.repository  List "com.downloader.app.data.repository  Long "com.downloader.app.data.repository  String "com.downloader.app.data.repository  DownloadDao 5com.downloader.app.data.repository.DownloadRepository  DownloadItem 5com.downloader.app.data.repository.DownloadRepository  DownloadStatus 5com.downloader.app.data.repository.DownloadRepository  Flow 5com.downloader.app.data.repository.DownloadRepository  Inject 5com.downloader.app.data.repository.DownloadRepository  Int 5com.downloader.app.data.repository.DownloadRepository  List 5com.downloader.app.data.repository.DownloadRepository  Long 5com.downloader.app.data.repository.DownloadRepository  String 5com.downloader.app.data.repository.DownloadRepository  downloadDao 5com.downloader.app.data.repository.DownloadRepository  	AppModule com.downloader.app.di  SingletonComponent com.downloader.app.di  ApplicationContext com.downloader.app.di.AppModule  ClipboardHelper com.downloader.app.di.AppModule  Context com.downloader.app.di.AppModule  NotificationHelper com.downloader.app.di.AppModule  Provides com.downloader.app.di.AppModule  	Singleton com.downloader.app.di.AppModule  ConcurrentHashMap com.downloader.app.service  CoroutineScope com.downloader.app.service  Dispatchers com.downloader.app.service  DownloadService com.downloader.app.service  InstagramExtractorService com.downloader.app.service  Int com.downloader.app.service  Job com.downloader.app.service  Long com.downloader.app.service  OkHttpClient com.downloader.app.service  Pattern com.downloader.app.service  Result com.downloader.app.service  String com.downloader.app.service  
SupervisorJob com.downloader.app.service  YouTubeExtractorService com.downloader.app.service  java com.downloader.app.service  ConcurrentHashMap *com.downloader.app.service.DownloadService  CoroutineScope *com.downloader.app.service.DownloadService  Dispatchers *com.downloader.app.service.DownloadService  DownloadItem *com.downloader.app.service.DownloadService  IBinder *com.downloader.app.service.DownloadService  Inject *com.downloader.app.service.DownloadService  Int *com.downloader.app.service.DownloadService  Intent *com.downloader.app.service.DownloadService  Job *com.downloader.app.service.DownloadService  Long *com.downloader.app.service.DownloadService  NotificationHelper *com.downloader.app.service.DownloadService  OkHttpClient *com.downloader.app.service.DownloadService  
SupervisorJob *com.downloader.app.service.DownloadService  getJAVA *com.downloader.app.service.DownloadService  getJava *com.downloader.app.service.DownloadService  java *com.downloader.app.service.DownloadService  ConcurrentHashMap 4com.downloader.app.service.DownloadService.Companion  CoroutineScope 4com.downloader.app.service.DownloadService.Companion  Dispatchers 4com.downloader.app.service.DownloadService.Companion  DownloadItem 4com.downloader.app.service.DownloadService.Companion  IBinder 4com.downloader.app.service.DownloadService.Companion  Inject 4com.downloader.app.service.DownloadService.Companion  Int 4com.downloader.app.service.DownloadService.Companion  Intent 4com.downloader.app.service.DownloadService.Companion  Job 4com.downloader.app.service.DownloadService.Companion  Long 4com.downloader.app.service.DownloadService.Companion  NotificationHelper 4com.downloader.app.service.DownloadService.Companion  OkHttpClient 4com.downloader.app.service.DownloadService.Companion  
SupervisorJob 4com.downloader.app.service.DownloadService.Companion  getJAVA 4com.downloader.app.service.DownloadService.Companion  getJava 4com.downloader.app.service.DownloadService.Companion  java 4com.downloader.app.service.DownloadService.Companion  ApplicationContext 4com.downloader.app.service.InstagramExtractorService  Context 4com.downloader.app.service.InstagramExtractorService  DownloadItem 4com.downloader.app.service.InstagramExtractorService  Inject 4com.downloader.app.service.InstagramExtractorService  InstagramContentType 4com.downloader.app.service.InstagramExtractorService  OkHttpClient 4com.downloader.app.service.InstagramExtractorService  Pattern 4com.downloader.app.service.InstagramExtractorService  Result 4com.downloader.app.service.InstagramExtractorService  String 4com.downloader.app.service.InstagramExtractorService  ApplicationContext >com.downloader.app.service.InstagramExtractorService.Companion  Context >com.downloader.app.service.InstagramExtractorService.Companion  DownloadItem >com.downloader.app.service.InstagramExtractorService.Companion  Inject >com.downloader.app.service.InstagramExtractorService.Companion  OkHttpClient >com.downloader.app.service.InstagramExtractorService.Companion  Pattern >com.downloader.app.service.InstagramExtractorService.Companion  Result >com.downloader.app.service.InstagramExtractorService.Companion  String >com.downloader.app.service.InstagramExtractorService.Companion  ApplicationContext 2com.downloader.app.service.YouTubeExtractorService  Context 2com.downloader.app.service.YouTubeExtractorService  DownloadItem 2com.downloader.app.service.YouTubeExtractorService  Inject 2com.downloader.app.service.YouTubeExtractorService  Result 2com.downloader.app.service.YouTubeExtractorService  String 2com.downloader.app.service.YouTubeExtractorService  ApplicationContext <com.downloader.app.service.YouTubeExtractorService.Companion  Context <com.downloader.app.service.YouTubeExtractorService.Companion  DownloadItem <com.downloader.app.service.YouTubeExtractorService.Companion  Inject <com.downloader.app.service.YouTubeExtractorService.Companion  Result <com.downloader.app.service.YouTubeExtractorService.Companion  String <com.downloader.app.service.YouTubeExtractorService.Companion  
APKPreview  com.downloader.app.ui.components  ArchivePreview  com.downloader.app.ui.components  
AttachFile  com.downloader.app.ui.components  AudioPreview  com.downloader.app.ui.components  Book  com.downloader.app.ui.components  Boolean  com.downloader.app.ui.components  Brush  com.downloader.app.ui.components  Build  com.downloader.app.ui.components  
CADPreview  com.downloader.app.ui.components  Code  com.downloader.app.ui.components  CodePreview  com.downloader.app.ui.components  
Composable  com.downloader.app.ui.components  DatabasePreview  com.downloader.app.ui.components  Description  com.downloader.app.ui.components  DocumentPreview  com.downloader.app.ui.components  DownloadCard  com.downloader.app.ui.components  DownloadProgress  com.downloader.app.ui.components  DownloadStatus  com.downloader.app.ui.components  EbookPreview  com.downloader.app.ui.components  ExecutablePreview  com.downloader.app.ui.components  ExperimentalMaterial3Api  com.downloader.app.ui.components  FileNotFoundMessage  com.downloader.app.ui.components  FilePreviewDialog  com.downloader.app.ui.components  FileTypePreview  com.downloader.app.ui.components  Float  com.downloader.app.ui.components  Folder  com.downloader.app.ui.components  FontPreview  com.downloader.app.ui.components  GenericFilePreview  com.downloader.app.ui.components  GridOn  com.downloader.app.ui.components  Image  com.downloader.app.ui.components  ImagePreview  com.downloader.app.ui.components  List  com.downloader.app.ui.components  
MaterialTheme  com.downloader.app.ui.components  	MusicNote  com.downloader.app.ui.components  OptIn  com.downloader.app.ui.components  	PlayArrow  com.downloader.app.ui.components  PresentationPreview  com.downloader.app.ui.components  RecentDownloads  com.downloader.app.ui.components  Settings  com.downloader.app.ui.components  	Slideshow  com.downloader.app.ui.components  SpreadsheetPreview  com.downloader.app.ui.components  Storage  com.downloader.app.ui.components  String  com.downloader.app.ui.components  
TextFormat  com.downloader.app.ui.components  Title  com.downloader.app.ui.components  Unit  com.downloader.app.ui.components  
UrlInputField  com.downloader.app.ui.components  VideoPreview  com.downloader.app.ui.components  ViewInAr  com.downloader.app.ui.components  androidx  com.downloader.app.ui.components  getFileTypeIcon  com.downloader.app.ui.components  getStatusColor  com.downloader.app.ui.components  
getStatusText  com.downloader.app.ui.components  openFileExternally  com.downloader.app.ui.components  	shareFile  com.downloader.app.ui.components  AboutScreen com.downloader.app.ui.screens  	AppHeader com.downloader.app.ui.screens  ClipboardDialog com.downloader.app.ui.screens  
Composable com.downloader.app.ui.screens  DeveloperSectionContent com.downloader.app.ui.screens  DownloadScreen com.downloader.app.ui.screens  DownloadsSection com.downloader.app.ui.screens  EmptyDownloadsView com.downloader.app.ui.screens  ExpandableCard com.downloader.app.ui.screens  ExperimentalMaterial3Api com.downloader.app.ui.screens  FeatureItem com.downloader.app.ui.screens  
FeatureRow com.downloader.app.ui.screens  FeaturesSectionContent com.downloader.app.ui.screens  FileSupportSectionContent com.downloader.app.ui.screens  Footer com.downloader.app.ui.screens  InfoItem com.downloader.app.ui.screens  InfoSectionContent com.downloader.app.ui.screens  List com.downloader.app.ui.screens  OptIn com.downloader.app.ui.screens  PrivacySectionContent com.downloader.app.ui.screens  String com.downloader.app.ui.screens  TechnicalSectionContent com.downloader.app.ui.screens  Unit com.downloader.app.ui.screens  UrlInputSection com.downloader.app.ui.screens  ImageVector )com.downloader.app.ui.screens.FeatureItem  String )com.downloader.app.ui.screens.FeatureItem  String &com.downloader.app.ui.screens.InfoItem  Boolean com.downloader.app.ui.theme  DarkColorScheme com.downloader.app.ui.theme  DownloaderTheme com.downloader.app.ui.theme  LightColorScheme com.downloader.app.ui.theme  Pink40 com.downloader.app.ui.theme  Pink80 com.downloader.app.ui.theme  Purple40 com.downloader.app.ui.theme  Purple80 com.downloader.app.ui.theme  PurpleGrey40 com.downloader.app.ui.theme  PurpleGrey80 com.downloader.app.ui.theme  
Typography com.downloader.app.ui.theme  Unit com.downloader.app.ui.theme  Boolean com.downloader.app.ui.viewmodel  DownloadUiState com.downloader.app.ui.viewmodel  DownloadViewModel com.downloader.app.ui.viewmodel  List com.downloader.app.ui.viewmodel  Long com.downloader.app.ui.viewmodel  MutableStateFlow com.downloader.app.ui.viewmodel  	StateFlow com.downloader.app.ui.viewmodel  String com.downloader.app.ui.viewmodel  asStateFlow com.downloader.app.ui.viewmodel  	emptyList com.downloader.app.ui.viewmodel  Boolean /com.downloader.app.ui.viewmodel.DownloadUiState  String /com.downloader.app.ui.viewmodel.DownloadUiState  ApplicationContext 1com.downloader.app.ui.viewmodel.DownloadViewModel  Boolean 1com.downloader.app.ui.viewmodel.DownloadViewModel  ClipboardHelper 1com.downloader.app.ui.viewmodel.DownloadViewModel  Context 1com.downloader.app.ui.viewmodel.DownloadViewModel  DownloadItem 1com.downloader.app.ui.viewmodel.DownloadViewModel  DownloadUiState 1com.downloader.app.ui.viewmodel.DownloadViewModel  Inject 1com.downloader.app.ui.viewmodel.DownloadViewModel  InstagramExtractorService 1com.downloader.app.ui.viewmodel.DownloadViewModel  List 1com.downloader.app.ui.viewmodel.DownloadViewModel  Long 1com.downloader.app.ui.viewmodel.DownloadViewModel  MutableStateFlow 1com.downloader.app.ui.viewmodel.DownloadViewModel  	StateFlow 1com.downloader.app.ui.viewmodel.DownloadViewModel  String 1com.downloader.app.ui.viewmodel.DownloadViewModel  YouTubeExtractorService 1com.downloader.app.ui.viewmodel.DownloadViewModel  _uiState 1com.downloader.app.ui.viewmodel.DownloadViewModel  asStateFlow 1com.downloader.app.ui.viewmodel.DownloadViewModel  	emptyList 1com.downloader.app.ui.viewmodel.DownloadViewModel  getASStateFlow 1com.downloader.app.ui.viewmodel.DownloadViewModel  getAsStateFlow 1com.downloader.app.ui.viewmodel.DownloadViewModel  getEMPTYList 1com.downloader.app.ui.viewmodel.DownloadViewModel  getEmptyList 1com.downloader.app.ui.viewmodel.DownloadViewModel  Array com.downloader.app.utils  Boolean com.downloader.app.utils  ClipboardHelper com.downloader.app.utils  Context com.downloader.app.utils  	FileUtils com.downloader.app.utils  Int com.downloader.app.utils  Long com.downloader.app.utils  NotificationHelper com.downloader.app.utils  PermissionHelper com.downloader.app.utils  String com.downloader.app.utils  android com.downloader.app.utils  ClipboardManager (com.downloader.app.utils.ClipboardHelper  Context (com.downloader.app.utils.ClipboardHelper  Flow (com.downloader.app.utils.ClipboardHelper  Inject (com.downloader.app.utils.ClipboardHelper  String (com.downloader.app.utils.ClipboardHelper  context (com.downloader.app.utils.ClipboardHelper  Boolean "com.downloader.app.utils.FileUtils  File "com.downloader.app.utils.FileUtils  FileType "com.downloader.app.utils.FileUtils  Long "com.downloader.app.utils.FileUtils  String "com.downloader.app.utils.FileUtils  Context +com.downloader.app.utils.NotificationHelper  DownloadItem +com.downloader.app.utils.NotificationHelper  Inject +com.downloader.app.utils.NotificationHelper  Int +com.downloader.app.utils.NotificationHelper  Long +com.downloader.app.utils.NotificationHelper  String +com.downloader.app.utils.NotificationHelper  android +com.downloader.app.utils.NotificationHelper  Context 5com.downloader.app.utils.NotificationHelper.Companion  DownloadItem 5com.downloader.app.utils.NotificationHelper.Companion  Inject 5com.downloader.app.utils.NotificationHelper.Companion  Int 5com.downloader.app.utils.NotificationHelper.Companion  Long 5com.downloader.app.utils.NotificationHelper.Companion  String 5com.downloader.app.utils.NotificationHelper.Companion  android 5com.downloader.app.utils.NotificationHelper.Companion  Array )com.downloader.app.utils.PermissionHelper  Boolean )com.downloader.app.utils.PermissionHelper  Context )com.downloader.app.utils.PermissionHelper  String )com.downloader.app.utils.PermissionHelper  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  File java.io  FileOutputStream java.io  IOException java.io  ActivityResultContracts 	java.lang  ConcurrentHashMap 	java.lang  Context 	java.lang  CoroutineScope 	java.lang  Dispatchers 	java.lang  DownloadStatus 	java.lang  DownloadUiState 	java.lang  ExperimentalMaterial3Api 	java.lang  MutableStateFlow 	java.lang  OkHttpClient 	java.lang  OnConflictStrategy 	java.lang  Pattern 	java.lang  SingletonComponent 	java.lang  
SupervisorJob 	java.lang  all 	java.lang  android 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  	emptyList 	java.lang  getValue 	java.lang  java 	java.lang  provideDelegate 	java.lang  
URLConnection java.net  
DecimalFormat 	java.text  SimpleDateFormat 	java.text  
AttachFile 	java.util  Book 	java.util  Brush 	java.util  Build 	java.util  Code 	java.util  
Composable 	java.util  Date 	java.util  Description 	java.util  DownloadStatus 	java.util  ExperimentalMaterial3Api 	java.util  Folder 	java.util  GridOn 	java.util  Image 	java.util  Locale 	java.util  
MaterialTheme 	java.util  	MusicNote 	java.util  	PlayArrow 	java.util  Settings 	java.util  	Slideshow 	java.util  Storage 	java.util  
TextFormat 	java.util  ViewInAr 	java.util  ConcurrentHashMap java.util.concurrent  CountDownLatch java.util.concurrent  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  Pattern java.util.regex  compile java.util.regex.Pattern  Inject javax.inject  	Singleton javax.inject  ActivityResultContracts kotlin  Any kotlin  Array kotlin  Boolean kotlin  ConcurrentHashMap kotlin  Context kotlin  CoroutineScope kotlin  Dispatchers kotlin  Double kotlin  DownloadStatus kotlin  DownloadUiState kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  Lazy kotlin  Long kotlin  MutableStateFlow kotlin  OkHttpClient kotlin  OnConflictStrategy kotlin  OptIn kotlin  Pattern kotlin  Result kotlin  SingletonComponent kotlin  String kotlin  
SupervisorJob kotlin  Unit kotlin  all kotlin  android kotlin  androidx kotlin  asStateFlow kotlin  	emptyList kotlin  getValue kotlin  java kotlin  provideDelegate kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  ActivityResultContracts kotlin.annotation  ConcurrentHashMap kotlin.annotation  Context kotlin.annotation  CoroutineScope kotlin.annotation  Dispatchers kotlin.annotation  DownloadStatus kotlin.annotation  DownloadUiState kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  MutableStateFlow kotlin.annotation  OkHttpClient kotlin.annotation  OnConflictStrategy kotlin.annotation  Pattern kotlin.annotation  Result kotlin.annotation  SingletonComponent kotlin.annotation  
SupervisorJob kotlin.annotation  all kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  	emptyList kotlin.annotation  getValue kotlin.annotation  java kotlin.annotation  provideDelegate kotlin.annotation  ActivityResultContracts kotlin.collections  ConcurrentHashMap kotlin.collections  Context kotlin.collections  CoroutineScope kotlin.collections  Dispatchers kotlin.collections  DownloadStatus kotlin.collections  DownloadUiState kotlin.collections  ExperimentalMaterial3Api kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableStateFlow kotlin.collections  OkHttpClient kotlin.collections  OnConflictStrategy kotlin.collections  Pattern kotlin.collections  Result kotlin.collections  SingletonComponent kotlin.collections  
SupervisorJob kotlin.collections  all kotlin.collections  android kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  	emptyList kotlin.collections  getValue kotlin.collections  java kotlin.collections  provideDelegate kotlin.collections  getALL kotlin.collections.Collection  getAll kotlin.collections.Collection  ActivityResultContracts kotlin.comparisons  ConcurrentHashMap kotlin.comparisons  Context kotlin.comparisons  CoroutineScope kotlin.comparisons  Dispatchers kotlin.comparisons  DownloadStatus kotlin.comparisons  DownloadUiState kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  MutableStateFlow kotlin.comparisons  OkHttpClient kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Pattern kotlin.comparisons  Result kotlin.comparisons  SingletonComponent kotlin.comparisons  
SupervisorJob kotlin.comparisons  all kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  	emptyList kotlin.comparisons  getValue kotlin.comparisons  java kotlin.comparisons  provideDelegate kotlin.comparisons  CoroutineContext kotlin.coroutines  plus 1kotlin.coroutines.AbstractCoroutineContextElement  ActivityResultContracts 	kotlin.io  ConcurrentHashMap 	kotlin.io  Context 	kotlin.io  CoroutineScope 	kotlin.io  Dispatchers 	kotlin.io  DownloadStatus 	kotlin.io  DownloadUiState 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  MutableStateFlow 	kotlin.io  OkHttpClient 	kotlin.io  OnConflictStrategy 	kotlin.io  Pattern 	kotlin.io  Result 	kotlin.io  SingletonComponent 	kotlin.io  
SupervisorJob 	kotlin.io  all 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  	emptyList 	kotlin.io  getValue 	kotlin.io  java 	kotlin.io  provideDelegate 	kotlin.io  ActivityResultContracts 
kotlin.jvm  ConcurrentHashMap 
kotlin.jvm  Context 
kotlin.jvm  CoroutineScope 
kotlin.jvm  Dispatchers 
kotlin.jvm  DownloadStatus 
kotlin.jvm  DownloadUiState 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OkHttpClient 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Pattern 
kotlin.jvm  Result 
kotlin.jvm  SingletonComponent 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  all 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  	emptyList 
kotlin.jvm  getValue 
kotlin.jvm  java 
kotlin.jvm  provideDelegate 
kotlin.jvm  ActivityResultContracts 
kotlin.ranges  ConcurrentHashMap 
kotlin.ranges  Context 
kotlin.ranges  CoroutineScope 
kotlin.ranges  Dispatchers 
kotlin.ranges  DownloadStatus 
kotlin.ranges  DownloadUiState 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OkHttpClient 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Pattern 
kotlin.ranges  Result 
kotlin.ranges  SingletonComponent 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  all 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  	emptyList 
kotlin.ranges  getValue 
kotlin.ranges  java 
kotlin.ranges  provideDelegate 
kotlin.ranges  KClass kotlin.reflect  ActivityResultContracts kotlin.sequences  ConcurrentHashMap kotlin.sequences  Context kotlin.sequences  CoroutineScope kotlin.sequences  Dispatchers kotlin.sequences  DownloadStatus kotlin.sequences  DownloadUiState kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  MutableStateFlow kotlin.sequences  OkHttpClient kotlin.sequences  OnConflictStrategy kotlin.sequences  Pattern kotlin.sequences  Result kotlin.sequences  SingletonComponent kotlin.sequences  
SupervisorJob kotlin.sequences  all kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  	emptyList kotlin.sequences  getValue kotlin.sequences  java kotlin.sequences  provideDelegate kotlin.sequences  ActivityResultContracts kotlin.text  ConcurrentHashMap kotlin.text  Context kotlin.text  CoroutineScope kotlin.text  Dispatchers kotlin.text  DownloadStatus kotlin.text  DownloadUiState kotlin.text  ExperimentalMaterial3Api kotlin.text  MutableStateFlow kotlin.text  OkHttpClient kotlin.text  OnConflictStrategy kotlin.text  Pattern kotlin.text  Result kotlin.text  SingletonComponent kotlin.text  
SupervisorJob kotlin.text  all kotlin.text  android kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  	emptyList kotlin.text  getValue kotlin.text  java kotlin.text  provideDelegate kotlin.text  CompletableJob kotlinx.coroutines  ConcurrentHashMap kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  OkHttpClient kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  delay kotlinx.coroutines  java kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  IO kotlinx.coroutines.Dispatchers  DownloadUiState kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  ConcurrentHashMap okhttp3  CoroutineScope okhttp3  Dispatchers okhttp3  DownloadUiState okhttp3  Job okhttp3  MutableStateFlow okhttp3  OkHttpClient okhttp3  Request okhttp3  	StateFlow okhttp3  
SupervisorJob okhttp3  asStateFlow okhttp3  	emptyList okhttp3  java okhttp3  Builder okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  followRedirects okhttp3.OkHttpClient.Builder  followSslRedirects okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 