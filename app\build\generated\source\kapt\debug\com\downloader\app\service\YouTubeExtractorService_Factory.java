package com.downloader.app.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class YouTubeExtractorService_Factory implements Factory<YouTubeExtractorService> {
  private final Provider<Context> contextProvider;

  public YouTubeExtractorService_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public YouTubeExtractorService get() {
    return newInstance(contextProvider.get());
  }

  public static YouTubeExtractorService_Factory create(Provider<Context> contextProvider) {
    return new YouTubeExtractorService_Factory(contextProvider);
  }

  public static YouTubeExtractorService newInstance(Context context) {
    return new YouTubeExtractorService(context);
  }
}
