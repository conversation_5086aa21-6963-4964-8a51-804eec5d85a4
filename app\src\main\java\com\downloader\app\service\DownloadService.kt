package com.downloader.app.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.data.model.DownloadStatus
import com.downloader.app.data.model.FileType
import com.downloader.app.utils.FileUtils
import com.downloader.app.utils.NotificationHelper
import kotlinx.coroutines.*
import okhttp3.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class DownloadService : Service() {
    
    
    @Inject
    lateinit var notificationHelper: NotificationHelper
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val activeDownloads = ConcurrentHashMap<Long, Job>()
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    companion object {
        const val ACTION_START_DOWNLOAD = "START_DOWNLOAD"
        const val ACTION_PAUSE_DOWNLOAD = "PAUSE_DOWNLOAD"
        const val ACTION_RESUME_DOWNLOAD = "RESUME_DOWNLOAD"
        const val ACTION_CANCEL_DOWNLOAD = "CANCEL_DOWNLOAD"
        const val EXTRA_DOWNLOAD_ID = "download_id"
        const val EXTRA_URL = "url"
        const val NOTIFICATION_ID = 1001
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_DOWNLOAD -> {
                val url = intent.getStringExtra(EXTRA_URL)
                if (url != null) {
                    val fileName = FileUtils.getFileNameFromUrl(url)
                    val mimeType = FileUtils.getMimeTypeFromUrl(url)
                    val fileType = FileType.fromMimeType(mimeType)
                    val downloadDir = FileUtils.getDownloadDirectory(fileType)
                    val uniqueFileName = FileUtils.getUniqueFileName(downloadDir, fileName)
                    val filePath = File(downloadDir, uniqueFileName).absolutePath

                    val downloadItem = DownloadItem(
                        url = url,
                        fileName = uniqueFileName,
                        filePath = filePath,
                        mimeType = mimeType,
                        status = DownloadStatus.PENDING
                    )
                    startDownload(downloadItem)
                }
            }
            ACTION_PAUSE_DOWNLOAD -> {
                val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
                if (downloadId != -1L) {
                    pauseDownload(downloadId)
                }
            }
            ACTION_RESUME_DOWNLOAD -> {
                val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
                if (downloadId != -1L) {
                    resumeDownload(downloadId)
                }
            }
            ACTION_CANCEL_DOWNLOAD -> {
                val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
                if (downloadId != -1L) {
                    cancelDownload(downloadId)
                }
            }
        }
        return START_NOT_STICKY
    }

private fun startDownload(downloadItem: DownloadItem) {
    val job = serviceScope.launch {
        try {
            // Start foreground service
            val notification = notificationHelper.createDownloadNotification(
                downloadItem, 0, "Starting download..."
            )
            startForeground(NOTIFICATION_ID, notification)

            downloadFile(downloadItem)

        } catch (e: Exception) {
            notificationHelper.showDownloadFailedNotification(
                downloadItem.id,
                e.message ?: "Download failed"
            )
        }
    }
    activeDownloads[downloadItem.id] = job
}
    
    private suspend fun downloadFile(downloadItem: DownloadItem) {
        val file = File(downloadItem.filePath)
        file.parentFile?.mkdirs()

        // Support resuming downloads by checking existing file size
        val downloadedBytes = if (file.exists()) file.length() else 0L

        val requestBuilder = Request.Builder()
            .url(downloadItem.url)

        if (downloadedBytes > 0) {
            requestBuilder.addHeader("Range", "bytes=$downloadedBytes-")
        }

        val request = requestBuilder.build()

        val response = okHttpClient.newCall(request).execute()

        if (!response.isSuccessful && response.code != 206) { // 206 Partial Content for range requests
            throw IOException("Failed to download file: ${response.code}")
        }

        val body = response.body ?: throw IOException("Response body is null")
        val contentLength = body.contentLength() + downloadedBytes

        // Update file size if we got it from headers
        // No database: update in memory if needed

        val inputStream = body.byteStream()
        val outputStream = FileOutputStream(file, downloadedBytes > 0) // append if resuming

        val buffer = ByteArray(8192)
        var totalBytesRead = downloadedBytes
        var lastUpdateTime = System.currentTimeMillis()
        var lastBytesRead = downloadedBytes

        try {
            // No database: update status in memory if needed

            while (true) {
                val bytesRead = inputStream.read(buffer)
                if (bytesRead == -1) break

                outputStream.write(buffer, 0, bytesRead)
                totalBytesRead += bytesRead

                val currentTime = System.currentTimeMillis()
                if (currentTime - lastUpdateTime >= 1000) { // Update every second
                    val speed = if (currentTime - lastUpdateTime > 0) {
                        ((totalBytesRead - lastBytesRead) * 1000) / (currentTime - lastUpdateTime)
                    } else 0L

                    val progress = if (contentLength > 0) {
                        ((totalBytesRead * 100) / contentLength).toInt()
                    } else 0

                    // No database: update progress in memory if needed

                    // Update notification
                    val notification = notificationHelper.createDownloadNotification(
                        downloadItem.copy(
                            progress = progress,
                            downloadedSize = totalBytesRead,
                            downloadSpeed = speed
                        ),
                        progress,
                        "Downloading... ${FileUtils.formatFileSize(totalBytesRead)}"
                    )
                    notificationHelper.updateNotification(NOTIFICATION_ID + downloadItem.id.toInt(), notification)

                    lastUpdateTime = currentTime
                    lastBytesRead = totalBytesRead
                }

                // Check if download was cancelled
                if (!activeDownloads.containsKey(downloadItem.id)) {
                    throw IOException("Download cancelled")
                }
            }

            // Download completed
            // No database: update status in memory if needed

            notificationHelper.showDownloadCompletedNotification(downloadItem.id, downloadItem.fileName)

        } finally {
            inputStream.close()
            outputStream.close()
            activeDownloads.remove(downloadItem.id)

            // Stop foreground service if no more active downloads
            if (activeDownloads.isEmpty()) {
                stopForeground(STOP_FOREGROUND_REMOVE)
                stopSelf()
            }
        }
    }
    
    private fun pauseDownload(id: Long) {
        activeDownloads[id]?.cancel()
        activeDownloads.remove(id)
        // Optionally update notification or status
    }

    private fun resumeDownload(id: Long) {
        // To resume, you need to retrieve the DownloadItem for the given id
        // This is a stub; actual implementation requires DownloadItem management
        // Example: startDownload(downloadItem)
    }

    private fun cancelDownload(id: Long) {
        activeDownloads[id]?.cancel()
        activeDownloads.remove(id)
        // Optionally update notification or status
    }
    
    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
        activeDownloads.clear()
    }
}
