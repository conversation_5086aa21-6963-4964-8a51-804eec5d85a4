{"logs": [{"outputFile": "com.downloader.app-mergeDebugResources-76:/values-fa/values-fa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7aa4dd35acc84f087f7df6becf4b1038\\transformed\\foundation-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,144", "endColumns": "88,88", "endOffsets": "139,228"}, "to": {"startLines": "181,182", "startColumns": "4,4", "startOffsets": "15747,15836", "endColumns": "88,88", "endOffsets": "15831,15920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1328e28f1361bb606dde64f609a081c\\transformed\\media3-ui-1.2.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,313,512,696,779,864,943,1036,1128,1205,1268,1360,1447,1510,1572,1633,1700,1816,1937,2057,2126,2202,2272,2344,2429,2516,2579,2653,2707,2776,2824,2885,2943,3020,3084,3148,3208,3270,3335,3401,3467,3519,3578,3651,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "308,507,691,774,859,938,1031,1123,1200,1263,1355,1442,1505,1567,1628,1695,1811,1932,2052,2121,2197,2267,2339,2424,2511,2574,2648,2702,2771,2819,2880,2938,3015,3079,3143,3203,3265,3330,3396,3462,3514,3573,3646,3719,3772"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,363,562,4604,4687,4772,4851,4944,5036,5113,5176,5268,5355,5418,5480,5541,5608,5724,5845,5965,6034,6110,6180,6252,6337,6424,6487,7211,7265,7334,7382,7443,7501,7578,7642,7706,7766,7828,7893,7959,8025,8077,8136,8209,8282", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "17,12,12,82,84,78,92,91,76,62,91,86,62,61,60,66,115,120,119,68,75,69,71,84,86,62,73,53,68,47,60,57,76,63,63,59,61,64,65,65,51,58,72,72,52", "endOffsets": "358,557,741,4682,4767,4846,4939,5031,5108,5171,5263,5350,5413,5475,5536,5603,5719,5840,5960,6029,6105,6175,6247,6332,6419,6482,6556,7260,7329,7377,7438,7496,7573,7637,7701,7761,7823,7888,7954,8020,8072,8131,8204,8277,8330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f05148d81958cc6d8e30b6d34a1ab13\\transformed\\media3-exoplayer-1.2.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,183,250,310,388,463,552,640", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "117,178,245,305,383,458,547,635,700"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6561,6628,6689,6756,6816,6894,6969,7058,7146", "endColumns": "66,60,66,59,77,74,88,87,64", "endOffsets": "6623,6684,6751,6811,6889,6964,7053,7141,7206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28c5dc97a63a31061752728abbdc10f0\\transformed\\appcompat-1.7.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,427,511,612,727,807,884,977,1072,1164,1258,1360,1455,1552,1646,1739,1829,1911,2019,2123,2221,2327,2432,2537,2694,2795", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "210,311,422,506,607,722,802,879,972,1067,1159,1253,1355,1450,1547,1641,1734,1824,1906,2014,2118,2216,2322,2427,2532,2689,2790,2872"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "746,856,957,1068,1152,1253,1368,1448,1525,1618,1713,1805,1899,2001,2096,2193,2287,2380,2470,2552,2660,2764,2862,2968,3073,3178,3335,15010", "endColumns": "109,100,110,83,100,114,79,76,92,94,91,93,101,94,96,93,92,89,81,107,103,97,105,104,104,156,100,81", "endOffsets": "851,952,1063,1147,1248,1363,1443,1520,1613,1708,1800,1894,1996,2091,2188,2282,2375,2465,2547,2655,2759,2857,2963,3068,3173,3330,3431,15087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\478b3be060432db7073f96b7c2278ef6\\transformed\\ui-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,192,271,365,463,549,631,734,819,902,983,1065,1139,1214,1288,1360,1435,1502", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,74,73,71,74,66,116", "endOffsets": "187,266,360,458,544,626,729,814,897,978,1060,1134,1209,1283,1355,1430,1497,1614"}, "to": {"startLines": "53,54,55,56,57,109,110,168,169,170,171,173,174,175,176,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4160,4247,4326,4420,4518,8335,8417,14679,14764,14847,14928,15092,15166,15241,15315,15488,15563,15630", "endColumns": "86,78,93,97,85,81,102,84,82,80,81,73,74,73,71,74,66,116", "endOffsets": "4242,4321,4415,4513,4599,8412,8515,14759,14842,14923,15005,15161,15236,15310,15382,15558,15625,15742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d62d9a540e552a1187e018192472b047\\transformed\\material3-release\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,280,394,512,608,704,818,957,1073,1208,1293,1396,1488,1585,1699,1822,1930,2063,2194,2316,2481,2603,2716,2832,2949,3042,3140,3261,3393,3500,3603,3708,3839,3975,4081,4191,4271,4364,4461,4582,4668,4752,4851,4933,5017,5118,5219,5316,5416,5503,5607,5707,5810,5930,6012,6116", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "165,275,389,507,603,699,813,952,1068,1203,1288,1391,1483,1580,1694,1817,1925,2058,2189,2311,2476,2598,2711,2827,2944,3037,3135,3256,3388,3495,3598,3703,3834,3970,4076,4186,4266,4359,4456,4577,4663,4747,4846,4928,5012,5113,5214,5311,5411,5498,5602,5702,5805,5925,6007,6111,6209"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8520,8635,8745,8859,8977,9073,9169,9283,9422,9538,9673,9758,9861,9953,10050,10164,10287,10395,10528,10659,10781,10946,11068,11181,11297,11414,11507,11605,11726,11858,11965,12068,12173,12304,12440,12546,12656,12736,12829,12926,13047,13133,13217,13316,13398,13482,13583,13684,13781,13881,13968,14072,14172,14275,14395,14477,14581", "endColumns": "114,109,113,117,95,95,113,138,115,134,84,102,91,96,113,122,107,132,130,121,164,121,112,115,116,92,97,120,131,106,102,104,130,135,105,109,79,92,96,120,85,83,98,81,83,100,100,96,99,86,103,99,102,119,81,103,97", "endOffsets": "8630,8740,8854,8972,9068,9164,9278,9417,9533,9668,9753,9856,9948,10045,10159,10282,10390,10523,10654,10776,10941,11063,11176,11292,11409,11502,11600,11721,11853,11960,12063,12168,12299,12435,12541,12651,12731,12824,12921,13042,13128,13212,13311,13393,13477,13578,13679,13776,13876,13963,14067,14167,14270,14390,14472,14576,14674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74d3d7c2d24a7100d6b0d87b145b1bf3\\transformed\\core-1.15.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "46,47,48,49,50,51,52,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3436,3535,3637,3736,3836,3937,4043,15387", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "3530,3632,3731,3831,3932,4038,4155,15483"}}]}]}