package com.downloader.app.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0014\b\u0086\u0081\u0002\u0018\u0000 \u00142\b\u0012\u0004\u0012\u00020\u00000\u0001:\u0001\u0014B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013\u00a8\u0006\u0015"}, d2 = {"Lcom/downloader/app/data/model/FileType;", "", "(Ljava/lang/String;I)V", "VIDEO", "AUDIO", "IMAGE", "DOCUMENT", "ARCHIVE", "APK", "EBOOK", "CODE", "FONT", "EXECUTABLE", "PRESENTATION", "SPREADSHEET", "DATABASE", "CAD", "VECTOR", "THREED_MODEL", "OTHER", "Companion", "app_release"})
public enum FileType {
    /*public static final*/ VIDEO /* = new VIDEO() */,
    /*public static final*/ AUDIO /* = new AUDIO() */,
    /*public static final*/ IMAGE /* = new IMAGE() */,
    /*public static final*/ DOCUMENT /* = new DOCUMENT() */,
    /*public static final*/ ARCHIVE /* = new ARCHIVE() */,
    /*public static final*/ APK /* = new APK() */,
    /*public static final*/ EBOOK /* = new EBOOK() */,
    /*public static final*/ CODE /* = new CODE() */,
    /*public static final*/ FONT /* = new FONT() */,
    /*public static final*/ EXECUTABLE /* = new EXECUTABLE() */,
    /*public static final*/ PRESENTATION /* = new PRESENTATION() */,
    /*public static final*/ SPREADSHEET /* = new SPREADSHEET() */,
    /*public static final*/ DATABASE /* = new DATABASE() */,
    /*public static final*/ CAD /* = new CAD() */,
    /*public static final*/ VECTOR /* = new VECTOR() */,
    /*public static final*/ THREED_MODEL /* = new THREED_MODEL() */,
    /*public static final*/ OTHER /* = new OTHER() */;
    @org.jetbrains.annotations.NotNull()
    public static final com.downloader.app.data.model.FileType.Companion Companion = null;
    
    FileType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.downloader.app.data.model.FileType> getEntries() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\u0006J\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u0004J\u000e\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u0004\u00a8\u0006\f"}, d2 = {"Lcom/downloader/app/data/model/FileType$Companion;", "", "()V", "fromFileName", "Lcom/downloader/app/data/model/FileType;", "fileName", "", "fromMimeType", "mimeType", "getDescription", "fileType", "getDisplayName", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.downloader.app.data.model.FileType fromMimeType(@org.jetbrains.annotations.NotNull()
        java.lang.String mimeType) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.downloader.app.data.model.FileType fromFileName(@org.jetbrains.annotations.NotNull()
        java.lang.String fileName) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDisplayName(@org.jetbrains.annotations.NotNull()
        com.downloader.app.data.model.FileType fileType) {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDescription(@org.jetbrains.annotations.NotNull()
        com.downloader.app.data.model.FileType fileType) {
            return null;
        }
    }
}