-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:71:9-79:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:75:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:73:13-64
	android:exported
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:74:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:72:13-62
manifest
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:2:1-83:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88d735e515a128009d2ccea67ed78829\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\164f987e23dba4e29cf0838f9d2e1167\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b59a273f17e2f2328de89ac7398ae73\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6473ba241fc8d06e2c8f9454c5d48c0a\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e2286c2e0090f544c5c68ec9b9000d0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28c5dc97a63a31061752728abbdc10f0\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media3:media3-ui:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1328e28f1361bb606dde64f609a081c\transformed\media3-ui-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bab6ab93b0229320cdff01290e752182\transformed\media3-extractor-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1586e7f0a1470c186aef59a68d9a004f\transformed\media3-container-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d169726efa060cb26f114c036849e525\transformed\media3-datasource-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9430f5cf3a670b24cf469a6c8d2978fe\transformed\media3-decoder-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b274b4e6eccac811a34b51e2e4a1c93d\transformed\media3-database-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21796d878df58dcab249ec6352b6e31e\transformed\media3-common-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f05148d81958cc6d8e30b6d34a1ab13\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\340557777bb0e8cfaa6a2f1998068efd\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2027c7d1aeca1cd1645d685f65ad703c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33c5185d776587941dbe4bd937b176c2\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9edffbe4151099dfc93aaf1697d21db\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eaf06e46581841a7a8a85fdefd8b654d\transformed\navigation-common-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\de37a3e5059d5af25fd93109397e5087\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\907fa42f6f26b06bb5934eab6f4b2446\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5fd2e37dc0b000600ce3a68c2bf1d638\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\88f218b00ea6f2b12aff3e20c92b2e43\transformed\navigation-compose-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69999a1f4bf047cf562aa7a51256e86b\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3d1fa2b822a5363b980c732fb23fc1b\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d62d9a540e552a1187e018192472b047\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\64a55cd43b448b759a7c3b21bcea63da\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4121fd3ebc3f61bec27155dd988610e7\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae916d189bfe6a664d47bb88dc92a63f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\51764831d77c0b780b04e97f23d1219f\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\3dbfcb97ba8c825efe5f2869cd1596e1\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7aa4dd35acc84f087f7df6becf4b1038\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e55a7a5510d8dcc0ebf6b781bbced2db\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\449fc21ea7540602dae8751bf431ab02\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\db8364c851f503be3bbfb2c87ac814fa\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\521ec23d5ec2c083d17e2d51b695c64e\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7be6bb6c01b2a41831c9bc07f813c1f6\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\78f15b7fde8c409ed1f2467251596296\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\5896d35c6783ae896c0749c992e3f857\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb7ca73e61962373954ac7aef0f2b6dc\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\25e66c178efea8acfd295bf88ce9f274\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\37052e3c6ed9bd3c0bf4a939d81ec392\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4c836a662d72609b8d8a1ab257f4051\transformed\activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\52e386261b8216cd30fbe5ed2ea18d89\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b663345c01d46f97ade444988d759139\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\35c06d0848012eae088e7bc81369bef8\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\5520295d841dd9ac5479b8fdfb6f2e95\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\55f681ea3d4bad0f955fdf67ae7b22ff\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0d1d2578d6501eaa4c5c131a5fc4e55\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e5cc6b909a0a66a1767234a29919878\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\68aaa90454ec9742e462e7d75666dc4d\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\1540d281ce33e276dcf8397133c79052\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2735da2ed0a6f3283bbef687f1221f6\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbaf5a4a45e3fa989559cfb88c0e0c48\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a92b283519324f5c7ae9a9873b82a84e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\958d699e2fdb4d60049c97c61ac8a5c3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\69e324b01a9e08fc4dcc8ccdcc6a05e9\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\4943a1b1e816bb424abc4eb2ef369dfa\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\20d95b1020b5e54c431880fd3cd1c581\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f2c236240d3ac1296a92186257e59a9\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\f85561c313d225d1feadca630539b4e7\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\c371254dfc18162569906377921d643a\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\478b3be060432db7073f96b7c2278ef6\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8af69a2704d0f21acf35fceb231eea17\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28aefb379186de514fb03bc514340fbf\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cebe35438d2be6bd111f5c50709e369\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8b8af7cfad676e6419bb083838f3b32\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdb80597a034d54d73913bdf9fb73662\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef4d55236c1c76dd392b9184af6e0265\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee76a187e225043d291a34cb24a60966\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c9d4c98495ef49518e4294f972224f33\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1e84748297ee603897d52d67fcbb79b\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffbb8e229a655f09c66ebc47ebb2be16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b9dad02baea5001f337e8fd494f001\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c47b36b258d5e9821e7b7b1514480cd9\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3a3592bb560d998feb19111b9b8b749\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\da8b00fc767240f52eab018d2b7233a2\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ef2a2969aa405b06dcf7ea2d99cafd4\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9470afb0757ad52ab1f3cab80f61fb27\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d322f28fa2dd744e5a7a329e9666a5eb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b109dc1efee1e7e5d71137cc079985d\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b689507f816dec62c16d87270b57ee3e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\421180a7ae52ac7690264f9c531798f0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a088b58985e0669f5f433ca0cae0ef30\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2803c8f15ad255abec012b9bf0988a4e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58fa078eec69b37ac4b603af415df0b6\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6e62e522cfe7367b97088399964a81d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\78af01a536c72b24a33aeb88e43c8f86\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c27dd34f86eeb0316cea11f17e49eb4\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.HaarigerHarald:android-youtubeExtractor:v2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1482c6baa3dccb5f5a6633c7e0044f91\transformed\android-youtubeExtractor-v2.1.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\021e5ac050a507de0efb08c92cdd60ca\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.github.evgenyneu:js-evaluator-for-android:v4.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d5fd859c965875a0bde10f19c5f512\transformed\js-evaluator-for-android-v4.0.0\AndroidManifest.xml:2:1-16:12
	package
		INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.github.HaarigerHarald:android-youtubeExtractor:v2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1482c6baa3dccb5f5a6633c7e0044f91\transformed\android-youtubeExtractor-v2.1.0\AndroidManifest.xml:11:5-67
MERGED from [com.github.HaarigerHarald:android-youtubeExtractor:v2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1482c6baa3dccb5f5a6633c7e0044f91\transformed\android-youtubeExtractor-v2.1.0\AndroidManifest.xml:11:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:15:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:15:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:16:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:16:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:17:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:17:22-72
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:20:5-79
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21796d878df58dcab249ec6352b6e31e\transformed\media3-common-1.2.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21796d878df58dcab249ec6352b6e31e\transformed\media3-common-1.2.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f05148d81958cc6d8e30b6d34a1ab13\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f05148d81958cc6d8e30b6d34a1ab13\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:22:5-79
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:20:22-76
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:23:5-77
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:23:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:24:5-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:24:22-84
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:27:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:27:22-74
uses-permission#android.permission.READ_CLIPBOARD
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:30:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:30:22-70
application
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:32:5-81:19
INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:32:5-81:19
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a088b58985e0669f5f433ca0cae0ef30\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a088b58985e0669f5f433ca0cae0ef30\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6e62e522cfe7367b97088399964a81d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6e62e522cfe7367b97088399964a81d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.github.HaarigerHarald:android-youtubeExtractor:v2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1482c6baa3dccb5f5a6633c7e0044f91\transformed\android-youtubeExtractor-v2.1.0\AndroidManifest.xml:13:5-16:19
MERGED from [com.github.HaarigerHarald:android-youtubeExtractor:v2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1482c6baa3dccb5f5a6633c7e0044f91\transformed\android-youtubeExtractor-v2.1.0\AndroidManifest.xml:13:5-16:19
MERGED from [com.github.evgenyneu:js-evaluator-for-android:v4.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d5fd859c965875a0bde10f19c5f512\transformed\js-evaluator-for-android-v4.0.0\AndroidManifest.xml:11:5-14:19
MERGED from [com.github.evgenyneu:js-evaluator-for-android:v4.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d5fd859c965875a0bde10f19c5f512\transformed\js-evaluator-for-android-v4.0.0\AndroidManifest.xml:11:5-14:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:39:9-51
	android:icon
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:37:9-46
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:43:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:40:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:38:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:36:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:44:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:34:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:41:9-48
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:35:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:42:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:33:9-40
activity#com.downloader.app.MainActivity
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:46:9-61:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:48:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:49:13-52
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:47:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:50:13-53:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:51:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:51:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:52:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:52:27-74
intent-filter#action:name:android.intent.action.SEND+category:name:android.intent.category.DEFAULT+data:mimeType:text/plain
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:56:13-60:29
action#android.intent.action.SEND
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:57:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:57:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:58:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:58:27-73
data
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:59:17-55
	android:mimeType
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:59:23-52
service#com.downloader.app.service.DownloadService
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:64:9-68:56
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:66:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:67:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:68:13-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:65:13-52
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:76:13-78:63
	android:resource
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:78:17-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:77:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88d735e515a128009d2ccea67ed78829\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\88d735e515a128009d2ccea67ed78829\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\164f987e23dba4e29cf0838f9d2e1167\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\164f987e23dba4e29cf0838f9d2e1167\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b59a273f17e2f2328de89ac7398ae73\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0b59a273f17e2f2328de89ac7398ae73\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6473ba241fc8d06e2c8f9454c5d48c0a\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6473ba241fc8d06e2c8f9454c5d48c0a\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e2286c2e0090f544c5c68ec9b9000d0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2e2286c2e0090f544c5c68ec9b9000d0\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28c5dc97a63a31061752728abbdc10f0\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28c5dc97a63a31061752728abbdc10f0\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media3:media3-ui:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1328e28f1361bb606dde64f609a081c\transformed\media3-ui-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b1328e28f1361bb606dde64f609a081c\transformed\media3-ui-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bab6ab93b0229320cdff01290e752182\transformed\media3-extractor-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bab6ab93b0229320cdff01290e752182\transformed\media3-extractor-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1586e7f0a1470c186aef59a68d9a004f\transformed\media3-container-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1586e7f0a1470c186aef59a68d9a004f\transformed\media3-container-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d169726efa060cb26f114c036849e525\transformed\media3-datasource-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d169726efa060cb26f114c036849e525\transformed\media3-datasource-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9430f5cf3a670b24cf469a6c8d2978fe\transformed\media3-decoder-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9430f5cf3a670b24cf469a6c8d2978fe\transformed\media3-decoder-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b274b4e6eccac811a34b51e2e4a1c93d\transformed\media3-database-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b274b4e6eccac811a34b51e2e4a1c93d\transformed\media3-database-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21796d878df58dcab249ec6352b6e31e\transformed\media3-common-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\21796d878df58dcab249ec6352b6e31e\transformed\media3-common-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f05148d81958cc6d8e30b6d34a1ab13\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f05148d81958cc6d8e30b6d34a1ab13\transformed\media3-exoplayer-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\340557777bb0e8cfaa6a2f1998068efd\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\340557777bb0e8cfaa6a2f1998068efd\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2027c7d1aeca1cd1645d685f65ad703c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2027c7d1aeca1cd1645d685f65ad703c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33c5185d776587941dbe4bd937b176c2\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\33c5185d776587941dbe4bd937b176c2\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9edffbe4151099dfc93aaf1697d21db\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f9edffbe4151099dfc93aaf1697d21db\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eaf06e46581841a7a8a85fdefd8b654d\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eaf06e46581841a7a8a85fdefd8b654d\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\de37a3e5059d5af25fd93109397e5087\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\de37a3e5059d5af25fd93109397e5087\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\907fa42f6f26b06bb5934eab6f4b2446\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\907fa42f6f26b06bb5934eab6f4b2446\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5fd2e37dc0b000600ce3a68c2bf1d638\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\5fd2e37dc0b000600ce3a68c2bf1d638\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\88f218b00ea6f2b12aff3e20c92b2e43\transformed\navigation-compose-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\88f218b00ea6f2b12aff3e20c92b2e43\transformed\navigation-compose-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69999a1f4bf047cf562aa7a51256e86b\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69999a1f4bf047cf562aa7a51256e86b\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3d1fa2b822a5363b980c732fb23fc1b\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\d3d1fa2b822a5363b980c732fb23fc1b\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d62d9a540e552a1187e018192472b047\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d62d9a540e552a1187e018192472b047\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\64a55cd43b448b759a7c3b21bcea63da\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\64a55cd43b448b759a7c3b21bcea63da\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4121fd3ebc3f61bec27155dd988610e7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\4121fd3ebc3f61bec27155dd988610e7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae916d189bfe6a664d47bb88dc92a63f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\ae916d189bfe6a664d47bb88dc92a63f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\51764831d77c0b780b04e97f23d1219f\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\51764831d77c0b780b04e97f23d1219f\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\3dbfcb97ba8c825efe5f2869cd1596e1\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\3dbfcb97ba8c825efe5f2869cd1596e1\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7aa4dd35acc84f087f7df6becf4b1038\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7aa4dd35acc84f087f7df6becf4b1038\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e55a7a5510d8dcc0ebf6b781bbced2db\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e55a7a5510d8dcc0ebf6b781bbced2db\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\449fc21ea7540602dae8751bf431ab02\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\449fc21ea7540602dae8751bf431ab02\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\db8364c851f503be3bbfb2c87ac814fa\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\db8364c851f503be3bbfb2c87ac814fa\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\521ec23d5ec2c083d17e2d51b695c64e\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\521ec23d5ec2c083d17e2d51b695c64e\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7be6bb6c01b2a41831c9bc07f813c1f6\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\7be6bb6c01b2a41831c9bc07f813c1f6\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\78f15b7fde8c409ed1f2467251596296\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\78f15b7fde8c409ed1f2467251596296\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\5896d35c6783ae896c0749c992e3f857\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\5896d35c6783ae896c0749c992e3f857\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb7ca73e61962373954ac7aef0f2b6dc\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\eb7ca73e61962373954ac7aef0f2b6dc\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\25e66c178efea8acfd295bf88ce9f274\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\25e66c178efea8acfd295bf88ce9f274\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\37052e3c6ed9bd3c0bf4a939d81ec392\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\37052e3c6ed9bd3c0bf4a939d81ec392\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4c836a662d72609b8d8a1ab257f4051\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4c836a662d72609b8d8a1ab257f4051\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\52e386261b8216cd30fbe5ed2ea18d89\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\52e386261b8216cd30fbe5ed2ea18d89\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b663345c01d46f97ade444988d759139\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b663345c01d46f97ade444988d759139\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\35c06d0848012eae088e7bc81369bef8\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\35c06d0848012eae088e7bc81369bef8\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\5520295d841dd9ac5479b8fdfb6f2e95\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\5520295d841dd9ac5479b8fdfb6f2e95\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\55f681ea3d4bad0f955fdf67ae7b22ff\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\55f681ea3d4bad0f955fdf67ae7b22ff\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0d1d2578d6501eaa4c5c131a5fc4e55\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0d1d2578d6501eaa4c5c131a5fc4e55\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e5cc6b909a0a66a1767234a29919878\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9e5cc6b909a0a66a1767234a29919878\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\68aaa90454ec9742e462e7d75666dc4d\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\68aaa90454ec9742e462e7d75666dc4d\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\1540d281ce33e276dcf8397133c79052\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\1540d281ce33e276dcf8397133c79052\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2735da2ed0a6f3283bbef687f1221f6\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2735da2ed0a6f3283bbef687f1221f6\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbaf5a4a45e3fa989559cfb88c0e0c48\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\dbaf5a4a45e3fa989559cfb88c0e0c48\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a92b283519324f5c7ae9a9873b82a84e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\a92b283519324f5c7ae9a9873b82a84e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\958d699e2fdb4d60049c97c61ac8a5c3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\958d699e2fdb4d60049c97c61ac8a5c3\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\69e324b01a9e08fc4dcc8ccdcc6a05e9\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\69e324b01a9e08fc4dcc8ccdcc6a05e9\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\4943a1b1e816bb424abc4eb2ef369dfa\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\4943a1b1e816bb424abc4eb2ef369dfa\transformed\lifecycle-livedata-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\20d95b1020b5e54c431880fd3cd1c581\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\20d95b1020b5e54c431880fd3cd1c581\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f2c236240d3ac1296a92186257e59a9\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f2c236240d3ac1296a92186257e59a9\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\f85561c313d225d1feadca630539b4e7\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\f85561c313d225d1feadca630539b4e7\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\c371254dfc18162569906377921d643a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\c371254dfc18162569906377921d643a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\478b3be060432db7073f96b7c2278ef6\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\478b3be060432db7073f96b7c2278ef6\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8af69a2704d0f21acf35fceb231eea17\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8af69a2704d0f21acf35fceb231eea17\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28aefb379186de514fb03bc514340fbf\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28aefb379186de514fb03bc514340fbf\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cebe35438d2be6bd111f5c50709e369\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1cebe35438d2be6bd111f5c50709e369\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8b8af7cfad676e6419bb083838f3b32\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d8b8af7cfad676e6419bb083838f3b32\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdb80597a034d54d73913bdf9fb73662\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fdb80597a034d54d73913bdf9fb73662\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef4d55236c1c76dd392b9184af6e0265\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ef4d55236c1c76dd392b9184af6e0265\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee76a187e225043d291a34cb24a60966\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee76a187e225043d291a34cb24a60966\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c9d4c98495ef49518e4294f972224f33\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c9d4c98495ef49518e4294f972224f33\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1e84748297ee603897d52d67fcbb79b\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\d1e84748297ee603897d52d67fcbb79b\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffbb8e229a655f09c66ebc47ebb2be16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffbb8e229a655f09c66ebc47ebb2be16\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b9dad02baea5001f337e8fd494f001\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\69b9dad02baea5001f337e8fd494f001\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c47b36b258d5e9821e7b7b1514480cd9\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\c47b36b258d5e9821e7b7b1514480cd9\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3a3592bb560d998feb19111b9b8b749\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\e3a3592bb560d998feb19111b9b8b749\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\da8b00fc767240f52eab018d2b7233a2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\da8b00fc767240f52eab018d2b7233a2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ef2a2969aa405b06dcf7ea2d99cafd4\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ef2a2969aa405b06dcf7ea2d99cafd4\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9470afb0757ad52ab1f3cab80f61fb27\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9470afb0757ad52ab1f3cab80f61fb27\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d322f28fa2dd744e5a7a329e9666a5eb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d322f28fa2dd744e5a7a329e9666a5eb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b109dc1efee1e7e5d71137cc079985d\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\2b109dc1efee1e7e5d71137cc079985d\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b689507f816dec62c16d87270b57ee3e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b689507f816dec62c16d87270b57ee3e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\421180a7ae52ac7690264f9c531798f0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\421180a7ae52ac7690264f9c531798f0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a088b58985e0669f5f433ca0cae0ef30\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a088b58985e0669f5f433ca0cae0ef30\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2803c8f15ad255abec012b9bf0988a4e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2803c8f15ad255abec012b9bf0988a4e\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58fa078eec69b37ac4b603af415df0b6\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\58fa078eec69b37ac4b603af415df0b6\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6e62e522cfe7367b97088399964a81d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f6e62e522cfe7367b97088399964a81d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\78af01a536c72b24a33aeb88e43c8f86\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\78af01a536c72b24a33aeb88e43c8f86\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c27dd34f86eeb0316cea11f17e49eb4\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\3c27dd34f86eeb0316cea11f17e49eb4\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.HaarigerHarald:android-youtubeExtractor:v2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1482c6baa3dccb5f5a6633c7e0044f91\transformed\android-youtubeExtractor-v2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.HaarigerHarald:android-youtubeExtractor:v2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1482c6baa3dccb5f5a6633c7e0044f91\transformed\android-youtubeExtractor-v2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\021e5ac050a507de0efb08c92cdd60ca\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.12\transforms\021e5ac050a507de0efb08c92cdd60ca\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.github.evgenyneu:js-evaluator-for-android:v4.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d5fd859c965875a0bde10f19c5f512\transformed\js-evaluator-for-android-v4.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.evgenyneu:js-evaluator-for-android:v4.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d5fd859c965875a0bde10f19c5f512\transformed\js-evaluator-for-android-v4.0.0\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a088b58985e0669f5f433ca0cae0ef30\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a088b58985e0669f5f433ca0cae0ef30\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:25-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.downloader.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.downloader.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
