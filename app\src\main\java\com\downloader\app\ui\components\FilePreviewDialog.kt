package com.downloader.app.ui.components

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.automirrored.filled.OpenInNew
import androidx.compose.material.icons.automirrored.filled.Launch
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.core.content.FileProvider
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import coil.compose.AsyncImage
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.data.model.FileType
import java.io.File

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilePreviewDialog(
    downloadItem: DownloadItem,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val file = File(downloadItem.filePath)
    val fileType = FileType.fromMimeType(downloadItem.mimeType)

    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth(0.95f)
                .fillMaxHeight(0.9f),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxSize()
            ) {
                // Header
                TopAppBar(
                    title = {
                        Text(
                            text = downloadItem.fileName,
                            fontWeight = FontWeight.Medium,
                            maxLines = 1
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = onDismiss) {
                            Icon(Icons.Default.Close, contentDescription = "Close")
                        }
                    },
                    actions = {
                        IconButton(
                            onClick = { openFileExternally(context, file, downloadItem.mimeType) }
                        ) {
                            Icon(Icons.AutoMirrored.Filled.OpenInNew, contentDescription = "Open externally")
                        }
                        IconButton(
                            onClick = { shareFile(context, file, downloadItem.mimeType) }
                        ) {
                            Icon(Icons.Default.Share, contentDescription = "Share")
                        }
                    }
                )

                // Content
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    when (fileType) {
                        FileType.IMAGE, FileType.VECTOR -> {
                            ImagePreview(file = file)
                        }
                        FileType.VIDEO -> {
                            VideoPreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.AUDIO -> {
                            AudioPreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.DOCUMENT -> {
                            DocumentPreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.PRESENTATION -> {
                            PresentationPreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.SPREADSHEET -> {
                            SpreadsheetPreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.EBOOK -> {
                            EbookPreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.CODE -> {
                            CodePreview(downloadItem = downloadItem)
                        }
                        FileType.ARCHIVE -> {
                            ArchivePreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.FONT -> {
                            FontPreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.DATABASE -> {
                            DatabasePreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.CAD, FileType.THREED_MODEL -> {
                            CADPreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.EXECUTABLE -> {
                            ExecutablePreview(downloadItem = downloadItem, context = context)
                        }
                        FileType.APK -> {
                            APKPreview(downloadItem = downloadItem, context = context)
                        }
                        else -> {
                            GenericFilePreview(downloadItem = downloadItem, context = context)
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ImagePreview(file: File) {
    if (file.exists()) {
        AsyncImage(
            model = file,
            contentDescription = "Image preview",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Fit
        )
    } else {
        FileNotFoundMessage()
    }
}

@Composable
private fun VideoPreview(downloadItem: DownloadItem, context: Context) {
    val exoPlayer = remember {
        ExoPlayer.Builder(context).build().apply {
            setMediaItem(MediaItem.fromUri(Uri.fromFile(File(downloadItem.filePath))))
            prepare()
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.release()
        }
    }

    AndroidView(
        factory = {
            PlayerView(it).apply {
                player = exoPlayer
            }
        },
        modifier = Modifier.fillMaxSize()
    )
}

@Composable
private fun AudioPreview(downloadItem: DownloadItem, context: Context) {
    val exoPlayer = remember {
        ExoPlayer.Builder(context).build().apply {
            setMediaItem(MediaItem.fromUri(Uri.fromFile(File(downloadItem.filePath))))
            prepare()
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.release()
        }
    }

    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Playing: ${downloadItem.fileName}",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(16.dp))
        AndroidView(
            factory = {
                PlayerView(it).apply {
                    player = exoPlayer
                    useController = true
                }
            }
        )
    }
}

@Composable
private fun CodePreview(downloadItem: DownloadItem) {
    val file = File(downloadItem.filePath)
    val code = remember(file) {
        runCatching { file.readText() }.getOrDefault("Could not read file")
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        Text(
            text = code,
            style = MaterialTheme.typography.bodyMedium
        )
    }
}

@Composable
private fun DocumentPreview(downloadItem: DownloadItem, context: Context) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            Icons.Default.Description,
            contentDescription = "Document",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Document",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = downloadItem.fileName,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
        ) {
            Icon(Icons.AutoMirrored.Filled.OpenInNew, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("Open Document")
        }
    }
}

@Composable
private fun GenericFilePreview(downloadItem: DownloadItem, context: Context) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            Icons.Default.AttachFile,
            contentDescription = "File",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "File",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = downloadItem.fileName,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(
            onClick = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
        ) {
            Icon(Icons.AutoMirrored.Filled.OpenInNew, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("Open File")
        }
    }
}

@Composable
private fun FileNotFoundMessage() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            Icons.Default.Error,
            contentDescription = "Error",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "File Not Found",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = "The file may have been moved or deleted",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

private fun openFileExternally(context: Context, file: File, mimeType: String) {
    try {
        val uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )
        
        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(uri, mimeType)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        
        context.startActivity(Intent.createChooser(intent, "Open with"))
    } catch (e: Exception) {
        // Handle error - could show a toast or snackbar
    }
}

@Composable
private fun PresentationPreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.Default.Slideshow,
        title = "Presentation",
        fileName = downloadItem.fileName,
        buttonText = "Open Presentation",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}

@Composable
private fun SpreadsheetPreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.Default.GridOn,
        title = "Spreadsheet",
        fileName = downloadItem.fileName,
        buttonText = "Open Spreadsheet",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}

@Composable
private fun EbookPreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.Default.Book,
        title = "eBook",
        fileName = downloadItem.fileName,
        buttonText = "Read eBook",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}



@Composable
private fun ArchivePreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.Default.Folder,
        title = "Archive",
        fileName = downloadItem.fileName,
        buttonText = "Extract Archive",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}

@Composable
private fun FontPreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.Default.FontDownload,
        title = "Font File",
        fileName = downloadItem.fileName,
        buttonText = "Install Font",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}

@Composable
private fun DatabasePreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.Default.Storage,
        title = "Database",
        fileName = downloadItem.fileName,
        buttonText = "Open Database",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}

@Composable
private fun CADPreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.Default.Architecture,
        title = "CAD/3D Model",
        fileName = downloadItem.fileName,
        buttonText = "Open Model",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}

@Composable
private fun ExecutablePreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.AutoMirrored.Filled.Launch,
        title = "Executable",
        fileName = downloadItem.fileName,
        buttonText = "View File",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}

@Composable
private fun APKPreview(downloadItem: DownloadItem, context: Context) {
    FileTypePreview(
        icon = Icons.Default.Settings,
        title = "Android App",
        fileName = downloadItem.fileName,
        buttonText = "Install APK",
        onOpen = { openFileExternally(context, File(downloadItem.filePath), downloadItem.mimeType) }
    )
}

@Composable
private fun FileTypePreview(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    fileName: String,
    buttonText: String,
    onOpen: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            icon,
            contentDescription = title,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = title,
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Medium
        )
        
        Text(
            text = fileName,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(onClick = onOpen) {
            Icon(Icons.AutoMirrored.Filled.OpenInNew, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text(buttonText)
        }
    }
}

private fun shareFile(context: Context, file: File, mimeType: String) {
    try {
        val uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )
        
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = mimeType
            putExtra(Intent.EXTRA_STREAM, uri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        
        context.startActivity(Intent.createChooser(intent, "Share file"))
    } catch (e: Exception) {
        // Handle error
    }
}
