<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.downloader.app.service</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.downloader.app.service</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.downloader.app.service</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">25</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">5</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">2.421s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">80%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.downloader.app.service.InstagramExtractorServiceTest.html">InstagramExtractorServiceTest</a>.
<a href="../classes/com.downloader.app.service.InstagramExtractorServiceTest.html#extractInstagramUrl should return failure for current implementation">extractInstagramUrl should return failure for current implementation</a>
</li>
<li>
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html">YouTubeExtractorServiceTest</a>.
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html#extractYouTubeUrl should handle timeout gracefully">extractYouTubeUrl should handle timeout gracefully</a>
</li>
<li>
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html">YouTubeExtractorServiceTest</a>.
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html#extractYouTubeUrl should normalize URL before extraction">extractYouTubeUrl should normalize URL before extraction</a>
</li>
<li>
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html">YouTubeExtractorServiceTest</a>.
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html#extractYouTubeUrl should validate input URL format">extractYouTubeUrl should validate input URL format</a>
</li>
<li>
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html">YouTubeExtractorServiceTest</a>.
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html#normalizeYouTubeUrl should handle edge cases">normalizeYouTubeUrl should handle edge cases</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/com.downloader.app.service.InstagramExtractorServiceTest.html">InstagramExtractorServiceTest</a>
</td>
<td>14</td>
<td>1</td>
<td>0</td>
<td>2.290s</td>
<td class="failures">92%</td>
</tr>
<tr>
<td class="failures">
<a href="../classes/com.downloader.app.service.YouTubeExtractorServiceTest.html">YouTubeExtractorServiceTest</a>
</td>
<td>11</td>
<td>4</td>
<td>0</td>
<td>0.131s</td>
<td class="failures">63%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.12</a> at Jul 27, 2025, 11:34:50 PM</p>
</div>
</div>
</body>
</html>
