package com.downloader.app.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.downloader.app.data.model.DownloadItem

@Composable
fun RecentDownloads(
    downloads: List<DownloadItem>,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        Text(
            text = "Recent Downloads",
            modifier = Modifier.padding(bottom = 8.dp)
        )
        LazyColumn {
            items(downloads) { download ->
                Text(text = download.fileName)
            }
        }
    }
}