package com.downloader.app.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ClipboardHelper_Factory implements Factory<ClipboardHelper> {
  private final Provider<Context> contextProvider;

  public ClipboardHelper_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ClipboardHelper get() {
    return newInstance(contextProvider.get());
  }

  public static ClipboardHelper_Factory create(Provider<Context> contextProvider) {
    return new ClipboardHelper_Factory(contextProvider);
  }

  public static ClipboardHelper newInstance(Context context) {
    return new ClipboardHelper(context);
  }
}
