package com.downloader.app.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0002\b\r\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\f\u0010\r\u001a\u00060\u000ej\u0002`\u000fH\u0007J\f\u0010\u0010\u001a\u00060\u000ej\u0002`\u000fH\u0007J\f\u0010\u0011\u001a\u00060\u000ej\u0002`\u000fH\u0007J\b\u0010\u0012\u001a\u00020\u000eH\u0007J\b\u0010\u0013\u001a\u00020\u000eH\u0007J\b\u0010\u0014\u001a\u00020\u000eH\u0007J\b\u0010\u0015\u001a\u00020\u000eH\u0007J\b\u0010\u0016\u001a\u00020\u000eH\u0007J\b\u0010\u0017\u001a\u00020\u000eH\u0007J\b\u0010\u0018\u001a\u00020\u000eH\u0007J\b\u0010\u0019\u001a\u00020\u000eH\u0007J\b\u0010\u001a\u001a\u00020\u000eH\u0007J\b\u0010\u001b\u001a\u00020\u000eH\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u0013\u0010\u0005\u001a\u00020\u00068G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/downloader/app/service/YouTubeExtractorServiceTest;", "", "()V", "context", "Landroid/content/Context;", "instantTaskExecutorRule", "Landroidx/arch/core/executor/testing/InstantTaskExecutorRule;", "getInstantTaskExecutorRule", "()Landroidx/arch/core/executor/testing/InstantTaskExecutorRule;", "testDispatcher", "Lkotlinx/coroutines/test/TestDispatcher;", "youTubeExtractorService", "Lcom/downloader/app/service/YouTubeExtractorService;", "extractYouTubeUrl should handle timeout gracefully", "", "Lkotlinx/coroutines/test/TestResult;", "extractYouTubeUrl should normalize URL before extraction", "extractYouTubeUrl should validate input URL format", "normalizeYouTubeUrl should handle YouTube Music URLs", "normalizeYouTubeUrl should handle YouTube Shorts URLs", "normalizeYouTubeUrl should handle YouTube Shorts URLs with parameters", "normalizeYouTubeUrl should handle edge cases", "normalizeYouTubeUrl should handle mobile YouTube URLs", "normalizeYouTubeUrl should handle youtu_be URLs with parameters", "normalizeYouTubeUrl should handle youtu_be short URLs", "normalizeYouTubeUrl should return original URL for standard YouTube URLs", "setup", "tearDown", "app_releaseUnitTest"})
@kotlin.OptIn(markerClass = {kotlinx.coroutines.ExperimentalCoroutinesApi.class})
public final class YouTubeExtractorServiceTest {
    @org.jetbrains.annotations.NotNull()
    private final androidx.arch.core.executor.testing.InstantTaskExecutorRule instantTaskExecutorRule = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.test.TestDispatcher testDispatcher = null;
    private android.content.Context context;
    private com.downloader.app.service.YouTubeExtractorService youTubeExtractorService;
    
    public YouTubeExtractorServiceTest() {
        super();
    }
    
    @org.junit.Rule()
    @org.jetbrains.annotations.NotNull()
    public final androidx.arch.core.executor.testing.InstantTaskExecutorRule getInstantTaskExecutorRule() {
        return null;
    }
    
    @org.junit.Before()
    public final void setup() {
    }
    
    @org.junit.After()
    public final void tearDown() {
    }
}