<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.downloader.app.ui.viewmodel.DownloadViewModelTest" tests="11" skipped="0" failures="5" errors="0" timestamp="2025-07-27T18:04:49" hostname="ADDY" time="0.621">
  <properties/>
  <testcase name="startDownload should handle YouTube extraction failure" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.47"/>
  <testcase name="initial state should be correct" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.017">
    <failure message="expected an empty string&#10;but was: null" type="com.google.common.truth.AssertionErrorWithFacts">expected an empty string
but was: null
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1$1.invokeSuspend(DownloadViewModelTest.kt:65)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1$1.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1.invokeSuspend(DownloadViewModelTest.kt:59)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.initial state should be correct(DownloadViewModelTest.kt:58)
</failure>
  </testcase>
  <testcase name="startDownload should handle direct file URL" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.018">
    <failure message="expected to be empty&#10;but was: Method getExternalStoragePublicDirectory in android.os.Environment not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details." type="com.google.common.truth.AssertionErrorWithFacts">expected to be empty
but was: Method getExternalStoragePublicDirectory in android.os.Environment not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1$1.invokeSuspend(DownloadViewModelTest.kt:167)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1$1.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1.invokeSuspend(DownloadViewModelTest.kt:165)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.startDownload should handle direct file URL(DownloadViewModelTest.kt:156)
</failure>
  </testcase>
  <testcase name="startDownload should handle Instagram URL with not supported message" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.028">
    <failure message="expected to contain: Instagram downloads are not yet supported&#10;but was            : Extracting Instagram content...please wait" type="com.google.common.truth.AssertionErrorWithFacts">expected to contain: Instagram downloads are not yet supported
but was            : Extracting Instagram content...please wait
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1$1.invokeSuspend(DownloadViewModelTest.kt:151)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1$1.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1.invokeSuspend(DownloadViewModelTest.kt:149)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.startDownload should handle Instagram URL with not supported message(DownloadViewModelTest.kt:142)
</failure>
  </testcase>
  <testcase name="clearMessage should clear success and error messages" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.018">
    <failure message="expected an empty string&#10;but was: null" type="com.google.common.truth.AssertionErrorWithFacts">expected an empty string
but was: null
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1$1.invokeSuspend(DownloadViewModelTest.kt:208)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1$1.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1.invokeSuspend(DownloadViewModelTest.kt:206)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.clearMessage should clear success and error messages(DownloadViewModelTest.kt:202)
</failure>
  </testcase>
  <testcase name="startDownload should show error for invalid URL" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.006"/>
  <testcase name="updateUrl should update current URL in state" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.007"/>
  <testcase name="clearHistory should clear downloads list" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.007"/>
  <testcase name="useClipboardUrl should update current URL from clipboard" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.005"/>
  <testcase name="dismissClipboardDialog should clear clipboard state" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.005"/>
  <testcase name="startDownload should handle YouTube URL" classname="com.downloader.app.ui.viewmodel.DownloadViewModelTest" time="0.038">
    <failure message="expected to contain: YouTube video extracted successfully&#10;but was            : Extracting YouTube video...please wait" type="com.google.common.truth.AssertionErrorWithFacts">expected to contain: YouTube video extracted successfully
but was            : Extracting YouTube video...please wait
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1$3.invokeSuspend(DownloadViewModelTest.kt:120)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1$3.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1$3.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1.invokeSuspend(DownloadViewModelTest.kt:118)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.startDownload should handle YouTube URL(DownloadViewModelTest.kt:98)
</failure>
  </testcase>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
