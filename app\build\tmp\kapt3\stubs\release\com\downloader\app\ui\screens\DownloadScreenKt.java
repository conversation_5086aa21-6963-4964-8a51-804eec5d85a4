package com.downloader.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\u001a,\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a*\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a\u009c\u0001\u0010\r\u001a\u00020\u00012\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u00132\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u00132\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u00132\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u00132\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u00132\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u0013H\u0003\u001a\b\u0010\u0019\u001a\u00020\u0001H\u0003\u001a2\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u00032\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00132\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u00a8\u0006\u001e"}, d2 = {"ClipboardDialog", "", "clipboardUrl", "", "onDismiss", "Lkotlin/Function0;", "onConfirm", "DownloadScreen", "viewModel", "Lcom/downloader/app/ui/viewmodel/DownloadViewModel;", "onNavigateToAbout", "modifier", "Landroidx/compose/ui/Modifier;", "DownloadsSection", "downloads", "", "Lcom/downloader/app/data/model/DownloadItem;", "onClearHistory", "onPause", "Lkotlin/Function1;", "onResume", "onCancel", "onRetry", "onDelete", "onPreview", "EmptyDownloadsView", "UrlInputSection", "url", "onUrlChange", "onDownload", "app_release"})
public final class DownloadScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DownloadScreen(@org.jetbrains.annotations.NotNull()
    com.downloader.app.ui.viewmodel.DownloadViewModel viewModel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToAbout, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ClipboardDialog(java.lang.String clipboardUrl, kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void UrlInputSection(java.lang.String url, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onUrlChange, kotlin.jvm.functions.Function0<kotlin.Unit> onDownload) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DownloadsSection(java.util.List<com.downloader.app.data.model.DownloadItem> downloads, kotlin.jvm.functions.Function0<kotlin.Unit> onClearHistory, kotlin.jvm.functions.Function1<? super com.downloader.app.data.model.DownloadItem, kotlin.Unit> onPause, kotlin.jvm.functions.Function1<? super com.downloader.app.data.model.DownloadItem, kotlin.Unit> onResume, kotlin.jvm.functions.Function1<? super com.downloader.app.data.model.DownloadItem, kotlin.Unit> onCancel, kotlin.jvm.functions.Function1<? super com.downloader.app.data.model.DownloadItem, kotlin.Unit> onRetry, kotlin.jvm.functions.Function1<? super com.downloader.app.data.model.DownloadItem, kotlin.Unit> onDelete, kotlin.jvm.functions.Function1<? super com.downloader.app.data.model.DownloadItem, kotlin.Unit> onPreview) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmptyDownloadsView() {
    }
}