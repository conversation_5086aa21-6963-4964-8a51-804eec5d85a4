{"logs": [{"outputFile": "com.downloader.app-mergeReleaseResources-72:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d62d9a540e552a1187e018192472b047\\transformed\\material3-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,284,393,507,604,705,823,960,1082,1234,1324,1420,1518,1620,1738,1861,1962,2094,2226,2355,2522,2644,2768,2895,3017,3116,3215,3336,3457,3560,3671,3779,3918,4062,4170,4276,4359,4457,4554,4667,4751,4836,4936,5016,5101,5198,5301,5398,5503,5593,5701,5804,5914,6032,6112,6217", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "166,279,388,502,599,700,818,955,1077,1229,1319,1415,1513,1615,1733,1856,1957,2089,2221,2350,2517,2639,2763,2890,3012,3111,3210,3331,3452,3555,3666,3774,3913,4057,4165,4271,4354,4452,4549,4662,4746,4831,4931,5011,5096,5193,5296,5393,5498,5588,5696,5799,5909,6027,6107,6212,6311"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8511,8627,8740,8849,8963,9060,9161,9279,9416,9538,9690,9780,9876,9974,10076,10194,10317,10418,10550,10682,10811,10978,11100,11224,11351,11473,11572,11671,11792,11913,12016,12127,12235,12374,12518,12626,12732,12815,12913,13010,13123,13207,13292,13392,13472,13557,13654,13757,13854,13959,14049,14157,14260,14370,14488,14568,14673", "endColumns": "115,112,108,113,96,100,117,136,121,151,89,95,97,101,117,122,100,131,131,128,166,121,123,126,121,98,98,120,120,102,110,107,138,143,107,105,82,97,96,112,83,84,99,79,84,96,102,96,104,89,107,102,109,117,79,104,98", "endOffsets": "8622,8735,8844,8958,9055,9156,9274,9411,9533,9685,9775,9871,9969,10071,10189,10312,10413,10545,10677,10806,10973,11095,11219,11346,11468,11567,11666,11787,11908,12011,12122,12230,12369,12513,12621,12727,12810,12908,13005,13118,13202,13287,13387,13467,13552,13649,13752,13849,13954,14044,14152,14255,14365,14483,14563,14668,14767"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28c5dc97a63a31061752728abbdc10f0\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,850,950,1059,1145,1250,1368,1454,1533,1624,1717,1812,1906,2000,2093,2189,2288,2379,2473,2553,2660,2761,2858,2964,3064,3162,3312,15108", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "845,945,1054,1140,1245,1363,1449,1528,1619,1712,1807,1901,1995,2088,2184,2283,2374,2468,2548,2655,2756,2853,2959,3059,3157,3307,3407,15184"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\478b3be060432db7073f96b7c2278ef6\\transformed\\ui-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,284,383,484,573,650,743,834,916,997,1079,1151,1227,1307,1381,1458,1530", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,75,79,73,76,71,121", "endOffsets": "194,279,378,479,568,645,738,829,911,992,1074,1146,1222,1302,1376,1453,1525,1647"}, "to": {"startLines": "53,54,55,56,57,109,110,168,169,170,171,173,174,175,176,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4146,4240,4325,4424,4525,8341,8418,14772,14863,14945,15026,15189,15261,15337,15417,15592,15669,15741", "endColumns": "93,84,98,100,88,76,92,90,81,80,81,71,75,79,73,76,71,121", "endOffsets": "4235,4320,4419,4520,4609,8413,8506,14858,14940,15021,15103,15256,15332,15412,15486,15664,15736,15858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74d3d7c2d24a7100d6b0d87b145b1bf3\\transformed\\core-1.15.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "46,47,48,49,50,51,52,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3412,3508,3610,3708,3813,3918,4030,15491", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "3503,3605,3703,3808,3913,4025,4141,15587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7aa4dd35acc84f087f7df6becf4b1038\\transformed\\foundation-release\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,89", "endOffsets": "140,230"}, "to": {"startLines": "181,182", "startColumns": "4,4", "startOffsets": "15863,15953", "endColumns": "89,89", "endOffsets": "15948,16038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1328e28f1361bb606dde64f609a081c\\transformed\\media3-ui-1.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,491,692,783,876,957,1053,1145,1216,1283,1372,1459,1527,1592,1655,1727,1820,1910,2001,2078,2160,2232,2303,2397,2493,2558,2622,2675,2733,2781,2842,2910,2982,3051,3123,3190,3245,3310,3376,3442,3494,3555,3630,3705", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,92,89,90,76,81,71,70,93,95,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "282,486,687,778,871,952,1048,1140,1211,1278,1367,1454,1522,1587,1650,1722,1815,1905,1996,2073,2155,2227,2298,2392,2488,2553,2617,2670,2728,2776,2837,2905,2977,3046,3118,3185,3240,3305,3371,3437,3489,3550,3625,3700,3757"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,541,4614,4705,4798,4879,4975,5067,5138,5205,5294,5381,5449,5514,5577,5649,5742,5832,5923,6000,6082,6154,6225,6319,6415,6480,7201,7254,7312,7360,7421,7489,7561,7630,7702,7769,7824,7889,7955,8021,8073,8134,8209,8284", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,92,89,90,76,81,71,70,93,95,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "332,536,737,4700,4793,4874,4970,5062,5133,5200,5289,5376,5444,5509,5572,5644,5737,5827,5918,5995,6077,6149,6220,6314,6410,6475,6539,7249,7307,7355,7416,7484,7556,7625,7697,7764,7819,7884,7950,8016,8068,8129,8204,8279,8336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f05148d81958cc6d8e30b6d34a1ab13\\transformed\\media3-exoplayer-1.2.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,241,308,385,455,549,641", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "115,174,236,303,380,450,544,636,707"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6544,6609,6668,6730,6797,6874,6944,7038,7130", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "6604,6663,6725,6792,6869,6939,7033,7125,7196"}}]}]}