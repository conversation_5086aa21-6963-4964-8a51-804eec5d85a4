package com.downloader.app.utils

import android.content.Context
import android.os.Environment
import android.webkit.MimeTypeMap
import com.downloader.app.data.model.FileType
import java.io.File
import java.net.URLConnection
import java.text.DecimalFormat
import java.util.Locale

object FileUtils {
    
    fun getFileNameFromUrl(url: String): String {
        return try {
            val fileName = url.substringAfterLast('/')
                .substringBefore('?')
                .substringBefore('#')
            
            if (fileName.isBlank() || !fileName.contains('.')) {
                "download_${System.currentTimeMillis()}"
            } else {
                fileName
            }
        } catch (e: Exception) {
            "download_${System.currentTimeMillis()}"
        }
    }
    
    fun getMimeTypeFromUrl(url: String): String {
        return try {
            val fileName = getFileNameFromUrl(url)
            val extension = fileName.substringAfterLast('.', "")
            
            MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension.lowercase())
                ?: URLConnection.guessContentTypeFromName(fileName)
                ?: "application/octet-stream"
        } catch (e: Exception) {
            "application/octet-stream"
        }
    }
    
    fun getDownloadDirectory(fileType: FileType): File {
        val baseDir = when (fileType) {
            FileType.VIDEO -> Environment.DIRECTORY_MOVIES
            FileType.AUDIO -> Environment.DIRECTORY_MUSIC
            FileType.IMAGE -> Environment.DIRECTORY_PICTURES
            FileType.DOCUMENT -> Environment.DIRECTORY_DOCUMENTS
            else -> Environment.DIRECTORY_DOWNLOADS
        }
        
        return File(
            Environment.getExternalStoragePublicDirectory(baseDir),
            "UniversalDownloader"
        ).apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }
    
    fun formatFileSize(bytes: Long): String {
        if (bytes <= 0) return "0 B"
        
        val units = arrayOf("B", "KB", "MB", "GB", "TB")
        val digitGroups = (Math.log10(bytes.toDouble()) / Math.log10(1024.0)).toInt()
        
        return DecimalFormat("#,##0.#").format(
            bytes / Math.pow(1024.0, digitGroups.toDouble())
        ) + " " + units[digitGroups]
    }
    
    fun formatSpeed(bytesPerSecond: Long): String {
        return "${formatFileSize(bytesPerSecond)}/s"
    }
    
    fun formatDuration(seconds: Long): String {
        if (seconds <= 0) return "Unknown"
        
        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val secs = seconds % 60
        
        return when {
            hours > 0 -> String.format(Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, secs)
            minutes > 0 -> String.format(Locale.getDefault(), "%02d:%02d", minutes, secs)
            else -> String.format(Locale.getDefault(), "00:%02d", secs)
        }
    }
    
    fun isValidUrl(url: String): Boolean {
        return try {
            val urlPattern = Regex(
                "^(https?://)?" + // protocol
                "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|" + // domain name
                "((\\d{1,3}\\.){3}\\d{1,3}))" + // OR ip (v4) address
                "(\\:\\d+)?(/[-a-z\\d%_.~+]*)*" + // port and path
                "(\\?[;&a-z\\d%_.~+=-]*)?" + // query string
                "(\\#[-a-z\\d_]*)?$", // fragment locator
                RegexOption.IGNORE_CASE
            )
            url.matches(urlPattern)
        } catch (e: Exception) {
            false
        }
    }
    
    fun sanitizeFileName(fileName: String): String {
        return fileName.replace(Regex("[^a-zA-Z0-9._-]"), "_")
            .replace(Regex("_{2,}"), "_")
            .trim('_')
    }
    
    fun getUniqueFileName(directory: File, fileName: String): String {
        val sanitizedName = sanitizeFileName(fileName)
        var file = File(directory, sanitizedName)
        var counter = 1
        
        val nameWithoutExtension = sanitizedName.substringBeforeLast('.')
        val extension = if (sanitizedName.contains('.')) {
            ".${sanitizedName.substringAfterLast('.')}"
        } else {
            ""
        }
        
        while (file.exists()) {
            val newName = "${nameWithoutExtension}_$counter$extension"
            file = File(directory, newName)
            counter++
        }
        
        return file.name
    }
}