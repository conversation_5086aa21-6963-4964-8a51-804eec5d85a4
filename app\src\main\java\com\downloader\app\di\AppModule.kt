package com.downloader.app.di

import android.content.Context
import com.downloader.app.utils.ClipboardHelper
import com.downloader.app.utils.NotificationHelper
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AppModule {
    
    @Provides
    @Singleton
    fun provideNotificationHelper(@ApplicationContext context: Context): NotificationHelper {
        return NotificationHelper(context)
    }
    
    @Provides
    @Singleton
    fun provideClipboardHelper(@ApplicationContext context: Context): ClipboardHelper {
        return ClipboardHelper(context)
    }
}