@echo off
echo ========================================
echo Universal Downloader - Test Suite
echo ========================================
echo.

echo [1/6] Cleaning project...
call gradlew clean
if %errorlevel% neq 0 (
    echo ✗ Clean failed
    pause
    exit /b 1
)
echo ✓ Project cleaned

echo.
echo [2/6] Running unit tests...
call gradlew test
if %errorlevel% neq 0 (
    echo ✗ Unit tests failed
    echo Check test reports at: app\build\reports\tests\testDebugUnitTest\index.html
    pause
    exit /b 1
)
echo ✓ Unit tests passed

echo.
echo [3/6] Running lint checks...
call gradlew lint
if %errorlevel% neq 0 (
    echo ⚠ Lint issues found
    echo Check lint report at: app\build\reports\lint-results.html
) else (
    echo ✓ Lint checks passed
)

echo.
echo [4/6] Building debug APK...
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo ✗ Debug build failed
    pause
    exit /b 1
)
echo ✓ Debug APK built successfully

echo.
echo [5/6] Running instrumented tests (requires connected device/emulator)...
echo Checking for connected devices...
adb devices | findstr "device$" >nul
if %errorlevel% neq 0 (
    echo ⚠ No Android device/emulator connected
    echo Skipping instrumented tests
    echo To run instrumented tests:
    echo   1. Connect an Android device or start an emulator
    echo   2. Run: gradlew connectedAndroidTest
) else (
    echo ✓ Device detected, running instrumented tests...
    call gradlew connectedAndroidTest
    if %errorlevel% neq 0 (
        echo ✗ Instrumented tests failed
        echo Check test reports at: app\build\reports\androidTests\connected\index.html
    ) else (
        echo ✓ Instrumented tests passed
    )
)

echo.
echo [6/6] Generating test reports...
echo.
echo ========================================
echo Test Results Summary:
echo ========================================

if exist "app\build\reports\tests\testDebugUnitTest\index.html" (
    echo ✓ Unit Test Report: app\build\reports\tests\testDebugUnitTest\index.html
) else (
    echo ✗ Unit Test Report: Not found
)

if exist "app\build\reports\androidTests\connected\index.html" (
    echo ✓ Instrumented Test Report: app\build\reports\androidTests\connected\index.html
) else (
    echo ⚠ Instrumented Test Report: Not available (no device connected)
)

if exist "app\build\reports\lint-results.html" (
    echo ✓ Lint Report: app\build\reports\lint-results.html
) else (
    echo ✗ Lint Report: Not found
)

if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo ✓ Debug APK: app\build\outputs\apk\debug\app-debug.apk
    for %%I in ("app\build\outputs\apk\debug\app-debug.apk") do echo   Size: %%~zI bytes
) else (
    echo ✗ Debug APK: Not found
)

echo.
echo ========================================
echo Test Coverage Information:
echo ========================================
echo.
echo To generate test coverage reports, run:
echo   gradlew testDebugUnitTestCoverage
echo   gradlew createDebugCoverageReport
echo.
echo Coverage reports will be available at:
echo   app\build\reports\coverage\test\debug\index.html
echo   app\build\reports\coverage\androidTest\debug\index.html
echo.

echo ========================================
echo Download App Testing Guide:
echo ========================================
echo.
echo Manual Testing URLs:
echo.
echo YouTube Videos:
echo   https://www.youtube.com/watch?v=dQw4w9WgXcQ
echo   https://youtu.be/dQw4w9WgXcQ
echo   https://www.youtube.com/shorts/abc123
echo.
echo Instagram Content:
echo   https://www.instagram.com/p/ABC123/
echo   https://www.instagram.com/reel/XYZ789/
echo   (Note: Instagram downloads show "not yet supported" message)
echo.
echo Direct File Downloads:
echo   https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4
echo   https://picsum.photos/800/600.jpg
echo   https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf
echo.
echo Testing Checklist:
echo   [ ] URL validation works for valid/invalid URLs
echo   [ ] YouTube URL detection and extraction
echo   [ ] Instagram URL detection (shows not supported message)
echo   [ ] Direct file downloads start successfully
echo   [ ] Clipboard monitoring detects URLs
echo   [ ] Download progress notifications appear
echo   [ ] Files are saved to correct directories
echo   [ ] Error handling works for network issues
echo   [ ] App doesn't crash with malformed URLs
echo.

echo ========================================
echo Test execution completed!
echo ========================================
echo.
pause
