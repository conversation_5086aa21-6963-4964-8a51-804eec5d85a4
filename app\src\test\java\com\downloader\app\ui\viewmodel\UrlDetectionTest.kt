package com.downloader.app.ui.viewmodel

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.downloader.app.service.InstagramExtractorService
import com.downloader.app.service.YouTubeExtractorService
import com.downloader.app.utils.ClipboardHelper
import com.google.common.truth.Truth.assertThat
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class UrlDetectionTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()
    
    private lateinit var clipboardHelper: ClipboardHelper
    private lateinit var youTubeExtractorService: YouTubeExtractorService
    private lateinit var instagramExtractorService: InstagramExtractorService
    private lateinit var context: Context
    private lateinit var viewModel: DownloadViewModel

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        clipboardHelper = mockk(relaxed = true)
        youTubeExtractorService = mockk(relaxed = true)
        instagramExtractorService = mockk(relaxed = true)
        context = mockk(relaxed = true)

        viewModel = DownloadViewModel(clipboardHelper, youTubeExtractorService, instagramExtractorService, context)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `should detect YouTube URLs correctly`() {
        // Test various YouTube URL formats
        val youtubeUrls = listOf(
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://music.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://www.youtube.com/shorts/dQw4w9WgXcQ",
            "https://youtube.com/shorts/dQw4w9WgXcQ"
        )

        youtubeUrls.forEach { url ->
            // Use reflection to access private method for testing
            val method = DownloadViewModel::class.java.getDeclaredMethod("isYouTubeUrl", String::class.java)
            method.isAccessible = true
            val result = method.invoke(viewModel, url) as Boolean
            
            assertThat(result).isTrue()
        }
    }

    @Test
    fun `should not detect non-YouTube URLs as YouTube`() {
        val nonYoutubeUrls = listOf(
            "https://vimeo.com/123456789",
            "https://instagram.com/p/ABC123/",
            "https://facebook.com/video/123",
            "https://example.com/video.mp4",
            "https://dailymotion.com/video/123",
            "https://twitch.tv/video/123"
        )

        nonYoutubeUrls.forEach { url ->
            val method = DownloadViewModel::class.java.getDeclaredMethod("isYouTubeUrl", String::class.java)
            method.isAccessible = true
            val result = method.invoke(viewModel, url) as Boolean
            
            assertThat(result).isFalse()
        }
    }

    @Test
    fun `should detect Instagram URLs correctly`() {
        val instagramUrls = listOf(
            "https://www.instagram.com/p/ABC123/",
            "https://instagram.com/p/ABC123/",
            "https://www.instagram.com/reel/ABC123/",
            "https://instagram.com/reel/ABC123/",
            "https://www.instagram.com/tv/ABC123/",
            "https://instagram.com/tv/ABC123/",
            "https://instagr.am/p/ABC123/"
        )

        instagramUrls.forEach { url ->
            val method = DownloadViewModel::class.java.getDeclaredMethod("isInstagramUrl", String::class.java)
            method.isAccessible = true
            val result = method.invoke(viewModel, url) as Boolean
            
            assertThat(result).isTrue()
        }
    }

    @Test
    fun `should not detect non-Instagram URLs as Instagram`() {
        val nonInstagramUrls = listOf(
            "https://youtube.com/watch?v=123",
            "https://facebook.com/video/123",
            "https://twitter.com/user/status/123",
            "https://example.com/video.mp4",
            "https://tiktok.com/@user/video/123"
        )

        nonInstagramUrls.forEach { url ->
            val method = DownloadViewModel::class.java.getDeclaredMethod("isInstagramUrl", String::class.java)
            method.isAccessible = true
            val result = method.invoke(viewModel, url) as Boolean
            
            assertThat(result).isFalse()
        }
    }

    @Test
    fun `should handle edge cases for URL detection`() {
        val edgeCases = listOf(
            "",
            " ",
            "not-a-url",
            "https://",
            "youtube",
            "instagram",
            "https://youtube",
            "https://instagram"
        )

        edgeCases.forEach { url ->
            // Test YouTube detection
            val youtubeMethod = DownloadViewModel::class.java.getDeclaredMethod("isYouTubeUrl", String::class.java)
            youtubeMethod.isAccessible = true
            val youtubeResult = youtubeMethod.invoke(viewModel, url) as Boolean
            assertThat(youtubeResult).isFalse()

            // Test Instagram detection
            val instagramMethod = DownloadViewModel::class.java.getDeclaredMethod("isInstagramUrl", String::class.java)
            instagramMethod.isAccessible = true
            val instagramResult = instagramMethod.invoke(viewModel, url) as Boolean
            assertThat(instagramResult).isFalse()
        }
    }

    @Test
    fun `should be case insensitive for URL detection`() {
        val mixedCaseUrls = mapOf(
            "HTTPS://WWW.YOUTUBE.COM/watch?v=123" to "youtube",
            "https://WWW.INSTAGRAM.COM/p/ABC/" to "instagram",
            "HTTPS://YOUTU.BE/123" to "youtube",
            "https://INSTAGR.AM/p/ABC/" to "instagram"
        )

        mixedCaseUrls.forEach { (url, platform) ->
            when (platform) {
                "youtube" -> {
                    val method = DownloadViewModel::class.java.getDeclaredMethod("isYouTubeUrl", String::class.java)
                    method.isAccessible = true
                    val result = method.invoke(viewModel, url) as Boolean
                    assertThat(result).isTrue()
                }
                "instagram" -> {
                    val method = DownloadViewModel::class.java.getDeclaredMethod("isInstagramUrl", String::class.java)
                    method.isAccessible = true
                    val result = method.invoke(viewModel, url) as Boolean
                    assertThat(result).isTrue()
                }
            }
        }
    }
}
