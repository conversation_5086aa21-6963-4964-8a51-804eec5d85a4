# 📱 Universal Downloader Android App - Tech Stack

This document outlines the complete tech stack and tools used in the development of Dawn, a universal download manager for Android.

---

## 💻 Programming Language
- **Kotlin** (Modern, concise, and preferred by Google for Android)

## 🧱 Architecture & Design Patterns
- **MVVM** (Model-View-ViewModel): Clear separation of concerns.
- **Clean Architecture**: Modular and scalable codebase with distinct layers (data, domain, UI).
- **Jetpack Libraries**: ViewModel, Navigation, LiveData (for some flows), StateFlow (for UI state).
- **Hilt**: For robust dependency injection.

---

## 🖼️ UI Development
- **Jetpack Compose**: Declarative UI toolkit for building native Android UIs.
- **Material 3 Design**: Modern, clean, and intuitive design components.
- **Scaffold**: Provides basic screen layout structure (TopAppBar, SnackbarHost, etc.).
- **TopAppBar**: For consistent app bar across screens.
- **SnackbarHost**: For displaying non-intrusive, temporary messages.
- **ExoPlayer**: For in-app video and audio previews.
- **Coil**: For efficient image loading and caching.

---

## 🔄 Download Handling
- **WorkManager**: For reliable background tasks and long-running downloads.
- **OkHttp**: For custom HTTP file download handling, providing fine-grained control over downloads.
- **Progress Monitoring**: `ResponseBody` streaming with custom progress bars for real-time updates.
- **Foreground Service**: Ensures large or persistent downloads continue uninterrupted in the background.

---

## 📂 File & Storage Access
- **MediaStore**: For saving videos/music/images in appropriate public directories, ensuring system visibility.
- **Scoped Storage**: Android 10+ compatible, providing secure and efficient access to app-specific and shared storage.
- **Storage Access Framework (SAF)**: For broader file access when user interaction is required.
- **FileProvider**: Securely shares downloaded files with other applications.
- **Permissions**: Runtime handling with `ActivityResultContracts` for modern Android permission management.

---

## 🔔 Notifications
- **NotificationCompat**: Custom download progress and status notifications with interactive controls.
- **Foreground Service**: Utilized for persistent notifications during active downloads.

---

## 🔍 File Metadata & Utilities
- **MediaMetadataRetriever**: For extracting video/audio thumbnails and metadata.
- **MIME Type Detection**: Using `URLConnection.guessContentTypeFromName()` and custom logic for accurate file type identification.
- **Clipboard Helper**: Utility for auto-detecting and processing URLs copied to the clipboard.
- **File Utilities**: Helper functions for file size formatting, path manipulation, etc.

---

## 🔒 Security & Privacy
- **No Data Collection**: The app is designed to be privacy-first, with no personal data collection or tracking.
- **Local Storage Only**: All downloaded files and app data are stored exclusively on the user's device.
- **Secure Operations**: Proper Android permission handling and secure file access mechanisms are implemented.
- **No Cloud Dependencies**: The app functions entirely offline, without reliance on external cloud services.

---

## 🧪 Testing
- **JUnit & MockK**: For unit testing business logic and ViewModel interactions.
- **Espresso & UI Automator**: For UI testing and end-to-end user flow validation.

---

## 🧩 Future Integrations & Enhancements
- **yt-dlp backend**: To support YouTube/Vimeo downloads via a potential backend service.
- **VirusTotal API**: To scan downloaded files for potential threats.
- **Firebase Remote Config**: For dynamic feature toggling and A/B testing.
- **Advanced Download Scheduling & Queue Management**.
- **Cloud Storage Integration**.
