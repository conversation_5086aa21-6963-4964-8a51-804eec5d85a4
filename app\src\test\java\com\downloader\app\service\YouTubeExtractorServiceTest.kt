package com.downloader.app.service

import android.content.Context
import android.util.Log
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.google.common.truth.Truth.assertThat
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class YouTubeExtractorServiceTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()
    
    private lateinit var context: Context
    private lateinit var youTubeExtractorService: YouTubeExtractorService

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)

        // Mock Android Log
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns 0
        every { Log.e(any(), any(), any()) } returns 0

        context = mockk(relaxed = true)
        youTubeExtractorService = YouTubeExtractorService(context)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `normalizeYouTubeUrl should handle youtu_be short URLs`() {
        // Use reflection to access private method
        val method = YouTubeExtractorService::class.java.getDeclaredMethod("normalizeYouTubeUrl", String::class.java)
        method.isAccessible = true
        
        val shortUrl = "https://youtu.be/dQw4w9WgXcQ"
        val result = method.invoke(youTubeExtractorService, shortUrl) as String
        
        assertThat(result).isEqualTo("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    }

    @Test
    fun `normalizeYouTubeUrl should handle youtu_be URLs with parameters`() {
        val method = YouTubeExtractorService::class.java.getDeclaredMethod("normalizeYouTubeUrl", String::class.java)
        method.isAccessible = true
        
        val shortUrlWithParams = "https://youtu.be/dQw4w9WgXcQ?t=30"
        val result = method.invoke(youTubeExtractorService, shortUrlWithParams) as String
        
        assertThat(result).isEqualTo("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    }

    @Test
    fun `normalizeYouTubeUrl should handle mobile YouTube URLs`() {
        val method = YouTubeExtractorService::class.java.getDeclaredMethod("normalizeYouTubeUrl", String::class.java)
        method.isAccessible = true
        
        val mobileUrl = "https://m.youtube.com/watch?v=dQw4w9WgXcQ"
        val result = method.invoke(youTubeExtractorService, mobileUrl) as String
        
        assertThat(result).isEqualTo("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    }

    @Test
    fun `normalizeYouTubeUrl should handle YouTube Music URLs`() {
        val method = YouTubeExtractorService::class.java.getDeclaredMethod("normalizeYouTubeUrl", String::class.java)
        method.isAccessible = true
        
        val musicUrl = "https://music.youtube.com/watch?v=dQw4w9WgXcQ&list=PLrAXtmRdnEQy"
        val result = method.invoke(youTubeExtractorService, musicUrl) as String
        
        assertThat(result).isEqualTo("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    }

    @Test
    fun `normalizeYouTubeUrl should handle YouTube Shorts URLs`() {
        val method = YouTubeExtractorService::class.java.getDeclaredMethod("normalizeYouTubeUrl", String::class.java)
        method.isAccessible = true
        
        val shortsUrl = "https://www.youtube.com/shorts/dQw4w9WgXcQ"
        val result = method.invoke(youTubeExtractorService, shortsUrl) as String
        
        assertThat(result).isEqualTo("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    }

    @Test
    fun `normalizeYouTubeUrl should handle YouTube Shorts URLs with parameters`() {
        val method = YouTubeExtractorService::class.java.getDeclaredMethod("normalizeYouTubeUrl", String::class.java)
        method.isAccessible = true
        
        val shortsUrlWithParams = "https://youtube.com/shorts/dQw4w9WgXcQ?feature=share"
        val result = method.invoke(youTubeExtractorService, shortsUrlWithParams) as String
        
        assertThat(result).isEqualTo("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
    }

    @Test
    fun `normalizeYouTubeUrl should return original URL for standard YouTube URLs`() {
        val method = YouTubeExtractorService::class.java.getDeclaredMethod("normalizeYouTubeUrl", String::class.java)
        method.isAccessible = true
        
        val standardUrl = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        val result = method.invoke(youTubeExtractorService, standardUrl) as String
        
        assertThat(result).isEqualTo(standardUrl)
    }

    @Test
    fun `normalizeYouTubeUrl should handle edge cases`() {
        val method = YouTubeExtractorService::class.java.getDeclaredMethod("normalizeYouTubeUrl", String::class.java)
        method.isAccessible = true

        // Test cases that should remain unchanged or be handled gracefully
        val edgeCases = listOf(
            "https://youtu.be/",
            "https://music.youtube.com/watch",
            "https://youtube.com/shorts/",
            ""
        )

        edgeCases.forEach { input ->
            val result = method.invoke(youTubeExtractorService, input) as String
            // Just verify the method doesn't crash and returns a string
            assertThat(result).isNotNull()
        }
    }

    @Test
    fun `extractYouTubeUrl should handle timeout gracefully`() = runTest {
        // This test would require more complex mocking of the YouTube extractor library
        // For now, we test that the method exists and can be called
        val invalidUrl = "https://www.youtube.com/watch?v=invalid"
        
        val result = youTubeExtractorService.extractYouTubeUrl(invalidUrl)
        
        // The actual result will depend on the YouTube extractor library
        // In a real scenario, this might timeout or return an error
        assertThat(result).isNotNull()
    }

    @Test
    fun `extractYouTubeUrl should validate input URL format`() = runTest {
        val invalidUrls = listOf(
            "",
            "not-a-url",
            "https://example.com",
            "https://vimeo.com/123456"
        )
        
        invalidUrls.forEach { url ->
            val result = youTubeExtractorService.extractYouTubeUrl(url)
            
            // Should handle invalid URLs gracefully
            assertThat(result).isNotNull()
        }
    }

    @Test
    fun `extractYouTubeUrl should normalize URL before extraction`() = runTest {
        // Test that the service normalizes URLs before attempting extraction
        val shortUrl = "https://youtu.be/dQw4w9WgXcQ"
        
        // This test would require mocking the YouTube extractor to verify
        // that the normalized URL is passed to the extractor
        val result = youTubeExtractorService.extractYouTubeUrl(shortUrl)
        
        assertThat(result).isNotNull()
    }
}
