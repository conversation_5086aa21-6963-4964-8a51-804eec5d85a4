package com.downloader.app.utils

import android.content.ClipboardManager
import android.content.Context
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ClipboardHelper @Inject constructor(
    private val context: Context
) {
    
    private val clipboardManager = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    private var lastClipboardText = ""
    
    fun monitorClipboard(): Flow<String> = flow {
        while (true) {
            try {
                val clipData = clipboardManager.primaryClip
                if (clipData != null && clipData.itemCount > 0) {
                    val text = clipData.getItemAt(0).text?.toString() ?: ""
                    
                    if (text != lastClipboardText && FileUtils.isValidUrl(text)) {
                        lastClipboardText = text
                        emit(text)
                    }
                }
            } catch (e: Exception) {
                // Ignore clipboard access errors
            }
            
            delay(1000) // Check every second
        }
    }
    
    fun getClipboardText(): String? {
        return try {
            val clipData = clipboardManager.primaryClip
            if (clipData != null && clipData.itemCount > 0) {
                clipData.getItemAt(0).text?.toString()
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    fun clearClipboard() {
        try {
            clipboardManager.clearPrimaryClip()
        } catch (e: Exception) {
            // Ignore errors
        }
    }
}