package com.downloader.app.ui.viewmodel

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.data.model.DownloadStatus
import com.downloader.app.data.model.FileType
import com.downloader.app.service.DownloadService
import com.downloader.app.service.InstagramExtractorService
import com.downloader.app.service.YouTubeExtractorService
import com.downloader.app.utils.ClipboardHelper
import com.downloader.app.utils.FileUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.io.File
import java.util.Date
import javax.inject.Inject
import okhttp3.*

@HiltViewModel
class DownloadViewModel @Inject constructor(
    private val clipboardHelper: ClipboardHelper,
    private val youTubeExtractorService: YouTubeExtractorService,
    private val instagramExtractorService: InstagramExtractorService,
    @ApplicationContext private val context: Context
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DownloadUiState())
    val uiState: StateFlow<DownloadUiState> = _uiState.asStateFlow()
    
    // No database: downloads list can be managed in memory if needed
    val downloads = MutableStateFlow<List<DownloadItem>>(emptyList())
    
    init {
        monitorClipboard()
    }
    
    private fun monitorClipboard() {
        viewModelScope.launch {
            clipboardHelper.monitorClipboard()
                .collect { url ->
                    _uiState.update { it.copy(clipboardUrl = url, showClipboardDialog = true) }
                }
        }
    }
    
    private fun isYouTubeUrl(url: String): Boolean {
        val lowerUrl = url.lowercase()
        return lowerUrl.contains("youtube.com") ||
               lowerUrl.contains("youtu.be") ||
               lowerUrl.contains("music.youtube.com") ||
               lowerUrl.contains("/shorts/")
    }

    private fun isInstagramUrl(url: String): Boolean {
        val lowerUrl = url.lowercase()
        return lowerUrl.contains("instagram.com") ||
               lowerUrl.contains("instagr.am")
    }
    
    private fun startYouTubeDownload(url: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(successMessage = "Extracting YouTube video...please wait") }
                
                val result = youTubeExtractorService.extractYouTubeUrl(url)
                
                result.fold(
                    onSuccess = { downloadItem ->
                        // Start download service
                        val intent = Intent(context, DownloadService::class.java).apply {
                            action = DownloadService.ACTION_START_DOWNLOAD
                            putExtra(DownloadService.EXTRA_URL, downloadItem.url)
                        }
                        context.startForegroundService(intent)
                        _uiState.update { it.copy(currentUrl = "", successMessage = "YouTube download started") }
                    },
                    onFailure = { error ->
                        val errorMsg = when {
                            error.message?.contains("No YouTube streams found") == true -> 
                                "This video cannot be downloaded. It may be restricted or unavailable."
                            error.message?.contains("network") == true || 
                            error.message?.contains("connection") == true -> 
                                "Network error. Please check your internet connection."
                            else -> "YouTube download error: ${error.message ?: "Unknown error"}"
                        }
                        _uiState.update { it.copy(errorMessage = errorMsg) }
                    }
                )
            } catch (e: Exception) {
                val errorMsg = when (e) {
                    is java.net.UnknownHostException -> 
                        "Network error. Please check your internet connection."
                    is java.util.concurrent.TimeoutException -> 
                        "Connection timed out. Please try again."
                    else -> "YouTube download error: ${e.message ?: "Unknown error"}"
                }
                _uiState.update { it.copy(errorMessage = errorMsg) }
            }
        }
    }

    private fun startInstagramDownload(url: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(successMessage = "Extracting Instagram content...please wait") }

                val result = instagramExtractorService.extractInstagramUrl(url)

                if (result.isSuccess) {
                    val downloadItem = result.getOrNull()!!

                    // Add to downloads list
                    downloads.update { currentList ->
                        currentList + downloadItem
                    }

                    // Start download service
                    val intent = Intent(context, DownloadService::class.java).apply {
                        action = DownloadService.ACTION_START_DOWNLOAD
                        putExtra(DownloadService.EXTRA_URL, downloadItem.url)
                    }
                    context.startForegroundService(intent)

                    _uiState.update { it.copy(successMessage = "Instagram content extracted successfully! Download started.") }
                } else {
                    val error = result.exceptionOrNull()
                    throw error ?: Exception("Instagram extraction failed")
                }
            } catch (e: Exception) {
                val errorMsg = when {
                    e.message?.contains("network", ignoreCase = true) == true ->
                        "Network error. Please check your connection."
                    e.message?.contains("timeout", ignoreCase = true) == true ->
                        "Connection timed out. Please try again."
                    e.message?.contains("additional implementation") == true ->
                        "Instagram downloads are not yet fully supported. Please use a direct video/image URL instead."
                    else -> "Instagram download error: ${e.message ?: "Unknown error"}"
                }
                _uiState.update { it.copy(errorMessage = errorMsg) }
            }
        }
    }

    fun updateUrl(url: String) {
        _uiState.update { it.copy(currentUrl = url) }
    }
    
    fun useClipboardUrl() {
        val clipboardUrl = _uiState.value.clipboardUrl
        if (clipboardUrl.isNotEmpty()) {
            _uiState.update { 
                it.copy(
                    currentUrl = clipboardUrl,
                    showClipboardDialog = false,
                    clipboardUrl = ""
                )
            }
        }
    }
    
    fun dismissClipboardDialog() {
        _uiState.update { it.copy(showClipboardDialog = false, clipboardUrl = "") }
    }
    
    fun startDownload() {
        val url = _uiState.value.currentUrl.trim()

        if (!FileUtils.isValidUrl(url)) {
            _uiState.update { it.copy(errorMessage = "Invalid URL") }
            return
        }

        if (isYouTubeUrl(url)) {
            startYouTubeDownload(url)
        } else if (isInstagramUrl(url)) {
            startInstagramDownload(url)
        } else {
            viewModelScope.launch {
                try {
                    val fileName = FileUtils.getFileNameFromUrl(url)
                    val mimeType = FileUtils.getMimeTypeFromUrl(url)
                    val fileType = FileType.fromMimeType(mimeType)
                    val downloadDir = FileUtils.getDownloadDirectory(fileType)
                    val uniqueFileName = FileUtils.getUniqueFileName(downloadDir, fileName)
                    val filePath = File(downloadDir, uniqueFileName).absolutePath

                    val downloadItem = DownloadItem(
                        url = url,
                        fileName = uniqueFileName,
                        filePath = filePath,
                        mimeType = mimeType,
                        status = DownloadStatus.PENDING,
                        createdAt = Date()
                    )

                    // Add to downloads list
                    downloads.update { currentList ->
                        currentList + downloadItem
                    }

                    // Start download service
                    val intent = Intent(context, DownloadService::class.java).apply {
                        action = DownloadService.ACTION_START_DOWNLOAD
                        putExtra(DownloadService.EXTRA_URL, url)
                    }
                    context.startForegroundService(intent)

                    _uiState.update { it.copy(currentUrl = "", successMessage = "Download started") }

                } catch (e: Exception) {
                    _uiState.update { it.copy(errorMessage = e.message ?: "Failed to start download") }
                }
            }
        }
    }

    fun clearMessage() {
        _uiState.update { it.copy(errorMessage = null, successMessage = null) }
    }
    
    fun clearHistory() {
        downloads.value = emptyList()
        _uiState.update { it.copy(successMessage = "Download history cleared") }
    }

    fun pauseDownload(id: Long) {
        _uiState.update { it.copy(successMessage = "Paused download $id") }
    }

    fun resumeDownload(id: Long) {
        _uiState.update { it.copy(successMessage = "Resumed download $id") }
    }

    fun cancelDownload(id: Long) {
        _uiState.update { it.copy(successMessage = "Cancelled download $id") }
    }

    fun retryDownload(item: DownloadItem) {
        _uiState.update { it.copy(successMessage = "Retrying download ${item.id}") }
    }

    fun deleteDownload(item: DownloadItem) {
        downloads.value = downloads.value.filter { it.id != item.id }
        _uiState.update { it.copy(successMessage = "Deleted download ${item.id}") }
    }
}

data class DownloadUiState(
    val currentUrl: String = "",
    val clipboardUrl: String = "",
    val showClipboardDialog: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null
)
