<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.downloader.app.utils.FileUtilsTest" tests="17" skipped="0" failures="3" errors="0" timestamp="2025-07-27T18:04:50" hostname="ADDY" time="0.04">
  <properties/>
  <testcase name="isValidUrl should return true for valid HTTPS URL" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="isValidUrl should return false for empty string" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getFileNameFromUrl should generate default name for URL without filename" classname="com.downloader.app.utils.FileUtilsTest" time="0.003"/>
  <testcase name="getMimeTypeFromUrl should return default mime type for unknown extension" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="isValidUrl should return true for valid HTTP URL" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="getFileNameFromUrl should handle URL with fragment" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="sanitizeFileName should handle multiple consecutive underscores" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="sanitizeFileName should trim leading and trailing underscores" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getFileNameFromUrl should extract filename from valid URL" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="isValidUrl should return true for URL without protocol" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getMimeTypeFromUrl should return correct mime type for audio" classname="com.downloader.app.utils.FileUtilsTest" time="0.015">
    <failure message="value of: getMimeTypeFromUrl(...)&#10;expected: audio/mpeg&#10;but was : application/octet-stream" type="com.google.common.truth.ComparisonFailureWithFacts">value of: getMimeTypeFromUrl(...)
expected: audio/mpeg
but was : application/octet-stream
	at app//com.downloader.app.utils.FileUtilsTest.getMimeTypeFromUrl should return correct mime type for audio(FileUtilsTest.kt:89)
</failure>
  </testcase>
  <testcase name="getMimeTypeFromUrl should return correct mime type for image" classname="com.downloader.app.utils.FileUtilsTest" time="0.009">
    <failure message="value of: getMimeTypeFromUrl(...)&#10;expected: image/jpeg&#10;but was : application/octet-stream" type="com.google.common.truth.ComparisonFailureWithFacts">value of: getMimeTypeFromUrl(...)
expected: image/jpeg
but was : application/octet-stream
	at app//com.downloader.app.utils.FileUtilsTest.getMimeTypeFromUrl should return correct mime type for image(FileUtilsTest.kt:101)
</failure>
  </testcase>
  <testcase name="getMimeTypeFromUrl should return correct mime type for video" classname="com.downloader.app.utils.FileUtilsTest" time="0.009">
    <failure message="value of: getMimeTypeFromUrl(...)&#10;expected: video/mp4&#10;but was : application/octet-stream" type="com.google.common.truth.ComparisonFailureWithFacts">value of: getMimeTypeFromUrl(...)
expected: video/mp4
but was : application/octet-stream
	at app//com.downloader.app.utils.FileUtilsTest.getMimeTypeFromUrl should return correct mime type for video(FileUtilsTest.kt:77)
</failure>
  </testcase>
  <testcase name="getFileNameFromUrl should handle URL with query parameters" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="isValidUrl should return false for invalid URL" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getFileNameFromUrl should generate default name for URL without extension" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="sanitizeFileName should replace invalid characters" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
