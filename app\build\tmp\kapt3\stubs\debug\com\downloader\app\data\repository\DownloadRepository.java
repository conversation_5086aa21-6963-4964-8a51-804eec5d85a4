package com.downloader.app.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0007\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0007J\u0016\u0010\b\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\f\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u0012\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\u00120\u0011J\u0018\u0010\u0013\u001a\u0004\u0018\u00010\n2\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u001c\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\n0\u00122\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u0016\u0010\u0018\u001a\u00020\u00152\u0006\u0010\t\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\u0019\u001a\u00020\u00062\u0006\u0010\t\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u000bJ&\u0010\u001a\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u001b\u001a\u00020\u001cH\u0086@\u00a2\u0006\u0002\u0010\u001dJ6\u0010\u001e\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\u00152\u0006\u0010\"\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010#J*\u0010$\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\r\u001a\u00020\u000e2\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u0015H\u0086@\u00a2\u0006\u0002\u0010&R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006'"}, d2 = {"Lcom/downloader/app/data/repository/DownloadRepository;", "", "downloadDao", "Lcom/downloader/app/data/database/DownloadDao;", "(Lcom/downloader/app/data/database/DownloadDao;)V", "clearAllDownloads", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteDownload", "download", "Lcom/downloader/app/data/model/DownloadItem;", "(Lcom/downloader/app/data/model/DownloadItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteDownloadsByStatus", "status", "Lcom/downloader/app/data/model/DownloadStatus;", "(Lcom/downloader/app/data/model/DownloadStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllDownloads", "Lkotlinx/coroutines/flow/Flow;", "", "getDownloadById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getDownloadsByStatus", "insertDownload", "updateDownload", "updateDownloadError", "errorMessage", "", "(JLcom/downloader/app/data/model/DownloadStatus;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDownloadProgress", "progress", "", "downloadedSize", "speed", "(JLcom/downloader/app/data/model/DownloadStatus;IJJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDownloadStatus", "completedAt", "(JLcom/downloader/app/data/model/DownloadStatus;Ljava/lang/Long;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class DownloadRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.downloader.app.data.database.DownloadDao downloadDao = null;
    
    @javax.inject.Inject()
    public DownloadRepository(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.database.DownloadDao downloadDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.downloader.app.data.model.DownloadItem>> getAllDownloads() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getDownloadById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.downloader.app.data.model.DownloadItem> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getDownloadsByStatus(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.downloader.app.data.model.DownloadItem>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertDownload(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem download, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateDownload(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem download, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateDownloadProgress(long id, @org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, int progress, long downloadedSize, long speed, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateDownloadStatus(long id, @org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, @org.jetbrains.annotations.Nullable()
    java.lang.Long completedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateDownloadError(long id, @org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteDownload(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem download, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearAllDownloads(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteDownloadsByStatus(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}