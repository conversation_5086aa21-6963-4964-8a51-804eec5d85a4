[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-debug-78:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-main-80:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-debug-78:\\xml_file_provider_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-main-80:\\xml\\file_provider_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-debug-78:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-main-80:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-debug-78:\\drawable_ic_dawn_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-main-80:\\drawable\\ic_dawn_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-debug-78:\\mipmap-hdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-main-80:\\mipmap-hdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-debug-78:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-main-80:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-debug-78:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.downloader.app-main-80:\\xml\\backup_rules.xml"}]