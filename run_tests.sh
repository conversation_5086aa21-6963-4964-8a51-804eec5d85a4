#!/bin/bash

echo "========================================"
echo "Universal Downloader - Test Suite"
echo "========================================"
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Make script executable
chmod +x "$0"

echo "[1/6] Cleaning project..."
./gradlew clean
if [ $? -ne 0 ]; then
    print_error "Clean failed"
    exit 1
fi
print_success "Project cleaned"

echo
echo "[2/6] Running unit tests..."
./gradlew test
if [ $? -ne 0 ]; then
    print_error "Unit tests failed"
    echo "Check test reports at: app/build/reports/tests/testDebugUnitTest/index.html"
    exit 1
fi
print_success "Unit tests passed"

echo
echo "[3/6] Running lint checks..."
./gradlew lint
if [ $? -ne 0 ]; then
    print_warning "Lint issues found"
    echo "Check lint report at: app/build/reports/lint-results.html"
else
    print_success "Lint checks passed"
fi

echo
echo "[4/6] Building debug APK..."
./gradlew assembleDebug
if [ $? -ne 0 ]; then
    print_error "Debug build failed"
    exit 1
fi
print_success "Debug APK built successfully"

echo
echo "[5/6] Running instrumented tests (requires connected device/emulator)..."
echo "Checking for connected devices..."
if command -v adb &> /dev/null; then
    if adb devices | grep -q "device$"; then
        print_success "Device detected, running instrumented tests..."
        ./gradlew connectedAndroidTest
        if [ $? -ne 0 ]; then
            print_error "Instrumented tests failed"
            echo "Check test reports at: app/build/reports/androidTests/connected/index.html"
        else
            print_success "Instrumented tests passed"
        fi
    else
        print_warning "No Android device/emulator connected"
        echo "Skipping instrumented tests"
        echo "To run instrumented tests:"
        echo "  1. Connect an Android device or start an emulator"
        echo "  2. Run: ./gradlew connectedAndroidTest"
    fi
else
    print_warning "ADB not found in PATH"
    echo "Skipping device detection and instrumented tests"
fi

echo
echo "[6/6] Generating test reports..."
echo
echo "========================================"
echo "Test Results Summary:"
echo "========================================"

if [ -f "app/build/reports/tests/testDebugUnitTest/index.html" ]; then
    print_success "Unit Test Report: app/build/reports/tests/testDebugUnitTest/index.html"
else
    print_error "Unit Test Report: Not found"
fi

if [ -f "app/build/reports/androidTests/connected/index.html" ]; then
    print_success "Instrumented Test Report: app/build/reports/androidTests/connected/index.html"
else
    print_warning "Instrumented Test Report: Not available (no device connected)"
fi

if [ -f "app/build/reports/lint-results.html" ]; then
    print_success "Lint Report: app/build/reports/lint-results.html"
else
    print_error "Lint Report: Not found"
fi

if [ -f "app/build/outputs/apk/debug/app-debug.apk" ]; then
    print_success "Debug APK: app/build/outputs/apk/debug/app-debug.apk"
    APK_SIZE=$(stat -f%z "app/build/outputs/apk/debug/app-debug.apk" 2>/dev/null || stat -c%s "app/build/outputs/apk/debug/app-debug.apk" 2>/dev/null)
    echo "  Size: $APK_SIZE bytes"
else
    print_error "Debug APK: Not found"
fi

echo
echo "========================================"
echo "Test Coverage Information:"
echo "========================================"
echo
echo "To generate test coverage reports, run:"
echo "  ./gradlew testDebugUnitTestCoverage"
echo "  ./gradlew createDebugCoverageReport"
echo
echo "Coverage reports will be available at:"
echo "  app/build/reports/coverage/test/debug/index.html"
echo "  app/build/reports/coverage/androidTest/debug/index.html"
echo

echo "========================================"
echo "Download App Testing Guide:"
echo "========================================"
echo
echo "Manual Testing URLs:"
echo
echo "YouTube Videos:"
echo "  https://www.youtube.com/watch?v=dQw4w9WgXcQ"
echo "  https://youtu.be/dQw4w9WgXcQ"
echo "  https://www.youtube.com/shorts/abc123"
echo
echo "Instagram Content:"
echo "  https://www.instagram.com/p/ABC123/"
echo "  https://www.instagram.com/reel/XYZ789/"
echo "  (Note: Instagram downloads show \"not yet supported\" message)"
echo
echo "Direct File Downloads:"
echo "  https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
echo "  https://picsum.photos/800/600.jpg"
echo "  https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
echo
echo "Testing Checklist:"
echo "  [ ] URL validation works for valid/invalid URLs"
echo "  [ ] YouTube URL detection and extraction"
echo "  [ ] Instagram URL detection (shows not supported message)"
echo "  [ ] Direct file downloads start successfully"
echo "  [ ] Clipboard monitoring detects URLs"
echo "  [ ] Download progress notifications appear"
echo "  [ ] Files are saved to correct directories"
echo "  [ ] Error handling works for network issues"
echo "  [ ] App doesn't crash with malformed URLs"
echo

echo "========================================"
echo "Test execution completed!"
echo "========================================"
echo

# Open test reports if on macOS
if [[ "$OSTYPE" == "darwin"* ]]; then
    if [ -f "app/build/reports/tests/testDebugUnitTest/index.html" ]; then
        echo "Opening test report in browser..."
        open "app/build/reports/tests/testDebugUnitTest/index.html"
    fi
fi
