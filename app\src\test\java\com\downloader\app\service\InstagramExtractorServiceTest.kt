package com.downloader.app.service

import android.content.Context
import android.util.Log
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.google.common.truth.Truth.assertThat
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@OptIn(ExperimentalCoroutinesApi::class)
class InstagramExtractorServiceTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()
    
    private lateinit var context: Context
    private lateinit var instagramExtractorService: InstagramExtractorService

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)

        // Mock Android Log
        mockkStatic(Log::class)
        every { Log.d(any(), any()) } returns 0
        every { Log.e(any(), any(), any()) } returns 0

        context = mockk(relaxed = true)
        instagramExtractorService = InstagramExtractorService(context)
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun `normalizeInstagramUrl should handle instagr_am short URLs`() {
        val method = InstagramExtractorService::class.java.getDeclaredMethod("normalizeInstagramUrl", String::class.java)
        method.isAccessible = true
        
        val shortUrl = "https://instagr.am/p/ABC123/"
        val result = method.invoke(instagramExtractorService, shortUrl) as String
        
        assertThat(result).isEqualTo("https://instagram.com/p/ABC123")
    }

    @Test
    fun `normalizeInstagramUrl should add HTTPS protocol`() {
        val method = InstagramExtractorService::class.java.getDeclaredMethod("normalizeInstagramUrl", String::class.java)
        method.isAccessible = true
        
        val urlWithoutProtocol = "instagram.com/p/ABC123/"
        val result = method.invoke(instagramExtractorService, urlWithoutProtocol) as String
        
        assertThat(result).isEqualTo("https://instagram.com/p/ABC123")
    }

    @Test
    fun `normalizeInstagramUrl should remove trailing slash`() {
        val method = InstagramExtractorService::class.java.getDeclaredMethod("normalizeInstagramUrl", String::class.java)
        method.isAccessible = true
        
        val urlWithTrailingSlash = "https://instagram.com/p/ABC123/"
        val result = method.invoke(instagramExtractorService, urlWithTrailingSlash) as String
        
        assertThat(result).isEqualTo("https://instagram.com/p/ABC123")
    }

    @Test
    fun `extractContentId should extract ID from post URLs`() {
        val method = InstagramExtractorService::class.java.getDeclaredMethod("extractContentId", String::class.java)
        method.isAccessible = true
        
        val postUrls = listOf(
            "https://instagram.com/p/ABC123/",
            "https://www.instagram.com/p/XYZ789/",
            "https://instagram.com/p/Test_123-456/"
        )
        
        val expectedIds = listOf("ABC123", "XYZ789", "Test_123-456")
        
        postUrls.forEachIndexed { index, url ->
            val result = method.invoke(instagramExtractorService, url) as String
            assertThat(result).isEqualTo(expectedIds[index])
        }
    }

    @Test
    fun `extractContentId should extract ID from reel URLs`() {
        val method = InstagramExtractorService::class.java.getDeclaredMethod("extractContentId", String::class.java)
        method.isAccessible = true
        
        val reelUrls = listOf(
            "https://instagram.com/reel/ABC123/",
            "https://www.instagram.com/reel/XYZ789/"
        )
        
        val expectedIds = listOf("ABC123", "XYZ789")
        
        reelUrls.forEachIndexed { index, url ->
            val result = method.invoke(instagramExtractorService, url) as String
            assertThat(result).isEqualTo(expectedIds[index])
        }
    }

    @Test
    fun `extractContentId should extract ID from TV URLs`() {
        val method = InstagramExtractorService::class.java.getDeclaredMethod("extractContentId", String::class.java)
        method.isAccessible = true
        
        val tvUrls = listOf(
            "https://instagram.com/tv/ABC123/",
            "https://www.instagram.com/tv/XYZ789/"
        )
        
        val expectedIds = listOf("ABC123", "XYZ789")
        
        tvUrls.forEachIndexed { index, url ->
            val result = method.invoke(instagramExtractorService, url) as String
            assertThat(result).isEqualTo(expectedIds[index])
        }
    }

    @Test
    fun `extractContentId should extract ID from story URLs`() {
        val method = InstagramExtractorService::class.java.getDeclaredMethod("extractContentId", String::class.java)
        method.isAccessible = true
        
        val storyUrls = listOf(
            "https://instagram.com/stories/username/123456789/",
            "https://www.instagram.com/stories/test.user/987654321/"
        )
        
        val expectedIds = listOf("username_123456789", "test.user_987654321")
        
        storyUrls.forEachIndexed { index, url ->
            val result = method.invoke(instagramExtractorService, url) as String
            assertThat(result).isEqualTo(expectedIds[index])
        }
    }

    @Test
    fun `extractContentId should return empty string for invalid URLs`() {
        val method = InstagramExtractorService::class.java.getDeclaredMethod("extractContentId", String::class.java)
        method.isAccessible = true
        
        val invalidUrls = listOf(
            "https://instagram.com/",
            "https://instagram.com/user/",
            "https://youtube.com/watch?v=123",
            "not-a-url"
        )
        
        invalidUrls.forEach { url ->
            val result = method.invoke(instagramExtractorService, url) as String
            assertThat(result).isEmpty()
        }
    }

    @Test
    fun `getInstagramContentType should identify post URLs`() {
        val postUrls = listOf(
            "https://instagram.com/p/ABC123/",
            "https://www.instagram.com/p/XYZ789/"
        )
        
        postUrls.forEach { url ->
            val result = instagramExtractorService.getInstagramContentType(url)
            assertThat(result).isEqualTo(InstagramExtractorService.InstagramContentType.POST)
        }
    }

    @Test
    fun `getInstagramContentType should identify reel URLs`() {
        val reelUrls = listOf(
            "https://instagram.com/reel/ABC123/",
            "https://www.instagram.com/reel/XYZ789/"
        )
        
        reelUrls.forEach { url ->
            val result = instagramExtractorService.getInstagramContentType(url)
            assertThat(result).isEqualTo(InstagramExtractorService.InstagramContentType.REEL)
        }
    }

    @Test
    fun `getInstagramContentType should identify TV URLs`() {
        val tvUrls = listOf(
            "https://instagram.com/tv/ABC123/",
            "https://www.instagram.com/tv/XYZ789/"
        )
        
        tvUrls.forEach { url ->
            val result = instagramExtractorService.getInstagramContentType(url)
            assertThat(result).isEqualTo(InstagramExtractorService.InstagramContentType.TV)
        }
    }

    @Test
    fun `getInstagramContentType should identify story URLs`() {
        val storyUrls = listOf(
            "https://instagram.com/stories/username/123456789/",
            "https://www.instagram.com/stories/test.user/987654321/"
        )
        
        storyUrls.forEach { url ->
            val result = instagramExtractorService.getInstagramContentType(url)
            assertThat(result).isEqualTo(InstagramExtractorService.InstagramContentType.STORY)
        }
    }

    @Test
    fun `getInstagramContentType should return UNKNOWN for invalid URLs`() {
        val invalidUrls = listOf(
            "https://instagram.com/",
            "https://youtube.com/watch?v=123",
            "not-a-url",
            "https://instagram.com/user/"
        )
        
        invalidUrls.forEach { url ->
            val result = instagramExtractorService.getInstagramContentType(url)
            assertThat(result).isEqualTo(InstagramExtractorService.InstagramContentType.UNKNOWN)
        }
    }

    @Test
    fun `extractInstagramUrl should return failure for current implementation`() = runTest {
        val instagramUrl = "https://instagram.com/p/ABC123/"
        
        val result = instagramExtractorService.extractInstagramUrl(instagramUrl)
        
        // Current implementation returns failure with message about additional implementation needed
        assertThat(result.isFailure).isTrue()
        assertThat(result.exceptionOrNull()?.message).contains("additional implementation")
    }
}
