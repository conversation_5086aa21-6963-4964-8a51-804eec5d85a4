<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">59</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">5</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">5.445s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">91%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Packages</a>
</li>
<li>
<a href="#tab2">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html">DownloadViewModelTest</a>.
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html#clearMessage should clear success and error messages">clearMessage should clear success and error messages</a>
</li>
<li>
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html">DownloadViewModelTest</a>.
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html#initial state should be correct">initial state should be correct</a>
</li>
<li>
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html">DownloadViewModelTest</a>.
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html#startDownload should handle Instagram URL with not supported message">startDownload should handle Instagram URL with not supported message</a>
</li>
<li>
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html">DownloadViewModelTest</a>.
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html#startDownload should handle YouTube URL">startDownload should handle YouTube URL</a>
</li>
<li>
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html">DownloadViewModelTest</a>.
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html#startDownload should handle direct file URL">startDownload should handle direct file URL</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.downloader.app.service.html">com.downloader.app.service</a>
</td>
<td>25</td>
<td>0</td>
<td>0</td>
<td>4.226s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.downloader.app.ui.viewmodel.html">com.downloader.app.ui.viewmodel</a>
</td>
<td>17</td>
<td>5</td>
<td>0</td>
<td>1.211s</td>
<td class="failures">70%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.downloader.app.utils.html">com.downloader.app.utils</a>
</td>
<td>17</td>
<td>0</td>
<td>0</td>
<td>0.008s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div id="tab2" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.downloader.app.service.InstagramExtractorServiceTest.html">com.downloader.app.service.InstagramExtractorServiceTest</a>
</td>
<td>14</td>
<td>0</td>
<td>0</td>
<td>0.355s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.downloader.app.service.YouTubeExtractorServiceTest.html">com.downloader.app.service.YouTubeExtractorServiceTest</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>3.871s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.downloader.app.ui.viewmodel.DownloadViewModelTest.html">com.downloader.app.ui.viewmodel.DownloadViewModelTest</a>
</td>
<td>11</td>
<td>5</td>
<td>0</td>
<td>1.206s</td>
<td class="failures">54%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.downloader.app.ui.viewmodel.UrlDetectionTest.html">com.downloader.app.ui.viewmodel.UrlDetectionTest</a>
</td>
<td>6</td>
<td>0</td>
<td>0</td>
<td>0.005s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.downloader.app.utils.FileUtilsTest.html">com.downloader.app.utils.FileUtilsTest</a>
</td>
<td>17</td>
<td>0</td>
<td>0</td>
<td>0.008s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.12</a> at Jul 27, 2025, 11:42:14 PM</p>
</div>
</div>
</body>
</html>
