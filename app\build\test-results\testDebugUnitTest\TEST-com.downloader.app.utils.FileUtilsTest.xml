<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.downloader.app.utils.FileUtilsTest" tests="17" skipped="0" failures="0" errors="0" timestamp="2025-07-27T18:12:13" hostname="ADDY" time="0.008">
  <properties/>
  <testcase name="isValidUrl should return true for valid HTTPS URL" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="isValidUrl should return false for empty string" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="getFileNameFromUrl should generate default name for URL without filename" classname="com.downloader.app.utils.FileUtilsTest" time="0.002"/>
  <testcase name="getMimeTypeFromUrl should return default mime type for unknown extension" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="isValidUrl should return true for valid HTTP URL" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getFileNameFromUrl should handle URL with fragment" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="sanitizeFileName should handle multiple consecutive underscores" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="sanitizeFileName should trim leading and trailing underscores" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getFileNameFromUrl should extract filename from valid URL" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getMimeTypeFromUrl should return a mime type for audio" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getMimeTypeFromUrl should return a mime type for image" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="getMimeTypeFromUrl should return a mime type for video" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="isValidUrl should return true for URL without protocol" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="getFileNameFromUrl should handle URL with query parameters" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="isValidUrl should return false for invalid URL" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <testcase name="getFileNameFromUrl should generate default name for URL without extension" classname="com.downloader.app.utils.FileUtilsTest" time="0.0"/>
  <testcase name="sanitizeFileName should replace invalid characters" classname="com.downloader.app.utils.FileUtilsTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
