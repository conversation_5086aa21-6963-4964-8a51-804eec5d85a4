package com.downloader.app.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.automirrored.filled.Launch
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.data.model.DownloadStatus
import com.downloader.app.data.model.FileType
import com.downloader.app.utils.FileUtils
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DownloadCard(
    downloadItem: DownloadItem,
    onPause: () -> Unit,
    onResume: () -> Unit,
    onCancel: () -> Unit,
    onRetry: () -> Unit,
    onDelete: () -> Unit,
    onPreview: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // File name and type icon
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = getFileTypeIcon(FileType.fromMimeType(downloadItem.mimeType)),
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = downloadItem.fileName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    Text(
                        text = getStatusText(downloadItem),
                        style = MaterialTheme.typography.bodySmall,
                        color = getStatusColor(downloadItem.status)
                    )
                }
                
                // Action buttons
                Row {
                    when (downloadItem.status) {
                        DownloadStatus.DOWNLOADING -> {
                            IconButton(onClick = onPause) {
                                Icon(Icons.Default.Stop, contentDescription = "Pause")
                            }
                            IconButton(onClick = onCancel) {
                                Icon(Icons.Default.Close, contentDescription = "Cancel")
                            }
                        }
                        DownloadStatus.PAUSED -> {
                            IconButton(onClick = onResume) {
                                Icon(Icons.Default.PlayArrow, contentDescription = "Resume")
                            }
                            IconButton(onClick = onCancel) {
                                Icon(Icons.Default.Close, contentDescription = "Cancel")
                            }
                        }
                        DownloadStatus.FAILED -> {
                            IconButton(onClick = onRetry) {
                                Icon(Icons.Default.Refresh, contentDescription = "Retry")
                            }
                            IconButton(onClick = onDelete) {
                                Icon(Icons.Default.Delete, contentDescription = "Delete")
                            }
                        }
                        DownloadStatus.COMPLETED -> {
                            IconButton(onClick = onPreview) {
                                Icon(Icons.Default.Visibility, contentDescription = "Preview")
                            }
                            IconButton(onClick = onDelete) {
                                Icon(Icons.Default.Delete, contentDescription = "Delete")
                            }
                        }
                        else -> {
                            IconButton(onClick = onDelete) {
                                Icon(Icons.Default.Delete, contentDescription = "Delete")
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Progress bar (only for active downloads)
            if (downloadItem.status == DownloadStatus.DOWNLOADING || downloadItem.status == DownloadStatus.PAUSED) {
                LinearProgressIndicator(
                    progress = { downloadItem.progress / 100f },
                    modifier = Modifier.fillMaxWidth(),
                    color = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Download info
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "${downloadItem.progress}%",
                        style = MaterialTheme.typography.bodySmall
                    )
                    
                    if (downloadItem.downloadSpeed > 0) {
                        Text(
                            text = FileUtils.formatSpeed(downloadItem.downloadSpeed),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
                
                if (downloadItem.fileSize > 0) {
                    Text(
                        text = "${FileUtils.formatFileSize(downloadItem.downloadedSize)} / ${FileUtils.formatFileSize(downloadItem.fileSize)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // File size and date
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                if (downloadItem.fileSize > 0) {
                    Text(
                        text = FileUtils.formatFileSize(downloadItem.fileSize),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Text(
                    text = SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault()).format(downloadItem.createdAt),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Error message
            if (downloadItem.status == DownloadStatus.FAILED && !downloadItem.errorMessage.isNullOrBlank()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = downloadItem.errorMessage,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
    }
}

@Composable
private fun getFileTypeIcon(fileType: FileType) = when (fileType) {
    FileType.VIDEO -> Icons.Default.PlayArrow
    FileType.AUDIO -> Icons.Default.MusicNote
    FileType.IMAGE -> Icons.Default.Image
    FileType.DOCUMENT -> Icons.Default.Description
    FileType.PRESENTATION -> Icons.Default.Slideshow
    FileType.SPREADSHEET -> Icons.Default.GridOn
    FileType.ARCHIVE -> Icons.Default.Folder
    FileType.EBOOK -> Icons.Default.Book
    FileType.CODE -> Icons.Default.Code
    FileType.FONT -> Icons.Default.TextFormat
    FileType.DATABASE -> Icons.Default.Storage
    FileType.CAD -> Icons.Default.Build
    FileType.VECTOR -> Icons.Default.Brush
    FileType.THREED_MODEL -> Icons.Default.ViewInAr
    FileType.EXECUTABLE -> Icons.AutoMirrored.Filled.Launch
    
    FileType.APK -> Icons.Default.Settings
    FileType.OTHER -> Icons.Default.AttachFile
}

@Composable
private fun getStatusColor(status: DownloadStatus) = when (status) {
    DownloadStatus.DOWNLOADING -> MaterialTheme.colorScheme.primary
    DownloadStatus.COMPLETED -> MaterialTheme.colorScheme.tertiary
    DownloadStatus.FAILED -> MaterialTheme.colorScheme.error
    DownloadStatus.PAUSED -> MaterialTheme.colorScheme.secondary
    DownloadStatus.CANCELLED -> MaterialTheme.colorScheme.outline
    DownloadStatus.PENDING -> MaterialTheme.colorScheme.onSurfaceVariant
}

private fun getStatusText(downloadItem: DownloadItem): String {
    return when (downloadItem.status) {
        DownloadStatus.PENDING -> "Pending"
        DownloadStatus.DOWNLOADING -> "Downloading"
        DownloadStatus.PAUSED -> "Paused"
        DownloadStatus.COMPLETED -> "Completed"
        DownloadStatus.FAILED -> "Failed"
        DownloadStatus.CANCELLED -> "Cancelled"
    }
}