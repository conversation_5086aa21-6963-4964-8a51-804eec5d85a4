<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.downloader.app.service.YouTubeExtractorServiceTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.downloader.app.service.YouTubeExtractorServiceTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.downloader.app.service.html">com.downloader.app.service</a> &gt; YouTubeExtractorServiceTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">11</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">4</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.131s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">63%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="extractYouTubeUrl should handle timeout gracefully"></a>
<h3 class="failures">extractYouTubeUrl should handle timeout gracefully</h3>
<span class="code">
<pre>java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at _COROUTINE._BOUNDARY._(CoroutineDebugging.kt:42)
	at com.downloader.app.service.YouTubeExtractorService.extractYouTubeUrl-gIAlu-s(YouTubeExtractorService.kt:33)
	at com.downloader.app.service.YouTubeExtractorServiceTest$extractYouTubeUrl should handle timeout gracefully$1.invokeSuspend(YouTubeExtractorServiceTest.kt:141)
	at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
Caused by: java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
</pre>
</span>
</div>
<div class="test">
<a name="extractYouTubeUrl should normalize URL before extraction"></a>
<h3 class="failures">extractYouTubeUrl should normalize URL before extraction</h3>
<span class="code">
<pre>java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at _COROUTINE._BOUNDARY._(CoroutineDebugging.kt:42)
	at com.downloader.app.service.YouTubeExtractorService.extractYouTubeUrl-gIAlu-s(YouTubeExtractorService.kt:33)
	at com.downloader.app.service.YouTubeExtractorServiceTest$extractYouTubeUrl should normalize URL before extraction$1.invokeSuspend(YouTubeExtractorServiceTest.kt:172)
	at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
Caused by: java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
</pre>
</span>
</div>
<div class="test">
<a name="extractYouTubeUrl should validate input URL format"></a>
<h3 class="failures">extractYouTubeUrl should validate input URL format</h3>
<span class="code">
<pre>java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at _COROUTINE._BOUNDARY._(CoroutineDebugging.kt:42)
	at com.downloader.app.service.YouTubeExtractorService.extractYouTubeUrl-gIAlu-s(YouTubeExtractorService.kt:33)
	at com.downloader.app.service.YouTubeExtractorServiceTest$extractYouTubeUrl should validate input URL format$1.invokeSuspend(YouTubeExtractorServiceTest.kt:158)
	at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
Caused by: java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
</pre>
</span>
</div>
<div class="test">
<a name="normalizeYouTubeUrl should handle edge cases"></a>
<h3 class="failures">normalizeYouTubeUrl should handle edge cases</h3>
<span class="code">
<pre>expected: https://youtu.be/
but was : https://www.youtube.com/watch?v=
	at app//com.downloader.app.service.YouTubeExtractorServiceTest.normalizeYouTubeUrl should handle edge cases(YouTubeExtractorServiceTest.kt:131)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="failures">extractYouTubeUrl should handle timeout gracefully</td>
<td class="failures">0.004s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">extractYouTubeUrl should normalize URL before extraction</td>
<td class="failures">0.007s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">extractYouTubeUrl should validate input URL format</td>
<td class="failures">0.003s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">normalizeYouTubeUrl should handle YouTube Music URLs</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalizeYouTubeUrl should handle YouTube Shorts URLs</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalizeYouTubeUrl should handle YouTube Shorts URLs with parameters</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">normalizeYouTubeUrl should handle edge cases</td>
<td class="failures">0.110s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">normalizeYouTubeUrl should handle mobile YouTube URLs</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalizeYouTubeUrl should handle youtu_be URLs with parameters</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalizeYouTubeUrl should handle youtu_be short URLs</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">normalizeYouTubeUrl should return original URL for standard YouTube URLs</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.12</a> at Jul 27, 2025, 11:34:50 PM</p>
</div>
</div>
</body>
</html>
