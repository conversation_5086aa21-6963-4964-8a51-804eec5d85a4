{"logs": [{"outputFile": "com.downloader.app-mergeReleaseResources-72:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28c5dc97a63a31061752728abbdc10f0\\transformed\\appcompat-1.7.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "708,816,922,1029,1118,1219,1337,1422,1502,1594,1688,1785,1879,1978,2072,2168,2263,2355,2447,2532,2639,2750,2852,2960,3068,3175,3340,15362", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "811,917,1024,1113,1214,1332,1417,1497,1589,1683,1780,1874,1973,2067,2163,2258,2350,2442,2527,2634,2745,2847,2955,3063,3170,3335,3434,15443"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f05148d81958cc6d8e30b6d34a1ab13\\transformed\\media3-exoplayer-1.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,194,261,332,414,496,591,680", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "125,189,256,327,409,491,586,675,754"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6599,6674,6738,6805,6876,6958,7040,7135,7224", "endColumns": "74,63,66,70,81,81,94,88,78", "endOffsets": "6669,6733,6800,6871,6953,7035,7130,7219,7298"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\478b3be060432db7073f96b7c2278ef6\\transformed\\ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,1004,1094,1170,1246,1325,1400,1476,1548", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,75,78,74,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,999,1089,1165,1241,1320,1395,1471,1543,1665"}, "to": {"startLines": "53,54,55,56,57,109,110,168,169,170,171,173,174,175,176,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4171,4266,4349,4446,4545,8432,8511,15009,15100,15187,15272,15448,15524,15600,15679,15855,15931,16003", "endColumns": "94,82,96,98,85,78,96,90,86,84,89,75,75,78,74,75,71,121", "endOffsets": "4261,4344,4441,4540,4626,8506,8603,15095,15182,15267,15357,15519,15595,15674,15749,15926,15998,16120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74d3d7c2d24a7100d6b0d87b145b1bf3\\transformed\\core-1.15.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "46,47,48,49,50,51,52,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3439,3536,3638,3737,3837,3944,4050,15754", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3531,3633,3732,3832,3939,4045,4166,15850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7aa4dd35acc84f087f7df6becf4b1038\\transformed\\foundation-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,88", "endOffsets": "137,226"}, "to": {"startLines": "181,182", "startColumns": "4,4", "startOffsets": "16125,16212", "endColumns": "86,88", "endOffsets": "16207,16296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1328e28f1361bb606dde64f609a081c\\transformed\\media3-ui-1.2.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,658,739,822,895,989,1079,1153,1220,1317,1414,1480,1549,1616,1687,1798,1909,2019,2086,2172,2245,2319,2406,2495,2559,2626,2679,2737,2785,2846,2911,2979,3044,3113,3177,3238,3304,3369,3435,3488,3548,3622,3696", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "280,466,653,734,817,890,984,1074,1148,1215,1312,1409,1475,1544,1611,1682,1793,1904,2014,2081,2167,2240,2314,2401,2490,2554,2621,2674,2732,2780,2841,2906,2974,3039,3108,3172,3233,3299,3364,3430,3483,3543,3617,3691,3750"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,4631,4712,4795,4868,4962,5052,5126,5193,5290,5387,5453,5522,5589,5660,5771,5882,5992,6059,6145,6218,6292,6379,6468,6532,7303,7356,7414,7462,7523,7588,7656,7721,7790,7854,7915,7981,8046,8112,8165,8225,8299,8373", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "17,12,12,80,82,72,93,89,73,66,96,96,65,68,66,70,110,110,109,66,85,72,73,86,88,63,66,52,57,47,60,64,67,64,68,63,60,65,64,65,52,59,73,73,58", "endOffsets": "330,516,703,4707,4790,4863,4957,5047,5121,5188,5285,5382,5448,5517,5584,5655,5766,5877,5987,6054,6140,6213,6287,6374,6463,6527,6594,7351,7409,7457,7518,7583,7651,7716,7785,7849,7910,7976,8041,8107,8160,8220,8294,8368,8427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d62d9a540e552a1187e018192472b047\\transformed\\material3-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,296,419,539,641,740,856,997,1115,1260,1344,1446,1544,1644,1759,1886,1993,2138,2282,2428,2620,2758,2879,3003,3129,3228,3325,3450,3588,3692,3805,3910,4056,4207,4317,4422,4508,4603,4698,4812,4902,4989,5090,5170,5254,5355,5460,5553,5653,5741,5851,5952,6057,6176,6256,6360", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "171,291,414,534,636,735,851,992,1110,1255,1339,1441,1539,1639,1754,1881,1988,2133,2277,2423,2615,2753,2874,2998,3124,3223,3320,3445,3583,3687,3800,3905,4051,4202,4312,4417,4503,4598,4693,4807,4897,4984,5085,5165,5249,5350,5455,5548,5648,5736,5846,5947,6052,6171,6251,6355,6451"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8608,8729,8849,8972,9092,9194,9293,9409,9550,9668,9813,9897,9999,10097,10197,10312,10439,10546,10691,10835,10981,11173,11311,11432,11556,11682,11781,11878,12003,12141,12245,12358,12463,12609,12760,12870,12975,13061,13156,13251,13365,13455,13542,13643,13723,13807,13908,14013,14106,14206,14294,14404,14505,14610,14729,14809,14913", "endColumns": "120,119,122,119,101,98,115,140,117,144,83,101,97,99,114,126,106,144,143,145,191,137,120,123,125,98,96,124,137,103,112,104,145,150,109,104,85,94,94,113,89,86,100,79,83,100,104,92,99,87,109,100,104,118,79,103,95", "endOffsets": "8724,8844,8967,9087,9189,9288,9404,9545,9663,9808,9892,9994,10092,10192,10307,10434,10541,10686,10830,10976,11168,11306,11427,11551,11677,11776,11873,11998,12136,12240,12353,12458,12604,12755,12865,12970,13056,13151,13246,13360,13450,13537,13638,13718,13802,13903,14008,14101,14201,14289,14399,14500,14605,14724,14804,14908,15004"}}]}]}