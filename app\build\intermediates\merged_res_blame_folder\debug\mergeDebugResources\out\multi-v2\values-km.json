{"logs": [{"outputFile": "com.downloader.app-mergeDebugResources-76:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7aa4dd35acc84f087f7df6becf4b1038\\transformed\\foundation-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,146", "endColumns": "90,91", "endOffsets": "141,233"}, "to": {"startLines": "181,182", "startColumns": "4,4", "startOffsets": "15858,15949", "endColumns": "90,91", "endOffsets": "15944,16036"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28c5dc97a63a31061752728abbdc10f0\\transformed\\appcompat-1.7.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "704,806,905,1015,1102,1205,1326,1404,1480,1571,1664,1756,1850,1950,2043,2138,2232,2323,2414,2497,2601,2705,2805,2914,3023,3132,3294,15112", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "801,900,1010,1097,1200,1321,1399,1475,1566,1659,1751,1845,1945,2038,2133,2227,2318,2409,2492,2596,2700,2800,2909,3018,3127,3289,3387,15191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74d3d7c2d24a7100d6b0d87b145b1bf3\\transformed\\core-1.15.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "46,47,48,49,50,51,52,177", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3392,3487,3590,3688,3788,3889,4001,15489", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3482,3585,3683,3783,3884,3996,4108,15585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f05148d81958cc6d8e30b6d34a1ab13\\transformed\\media3-exoplayer-1.2.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,185,251,320,393,464,562,657", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "121,180,246,315,388,459,557,652,721"}, "to": {"startLines": "82,83,84,85,86,87,88,89,90", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6525,6596,6655,6721,6790,6863,6934,7032,7127", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "6591,6650,6716,6785,6858,6929,7027,7122,7191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1328e28f1361bb606dde64f609a081c\\transformed\\media3-ui-1.2.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,470,654,731,807,886,970,1058,1139,1205,1292,1381,1446,1508,1570,1636,1763,1886,2009,2081,2166,2237,2320,2402,2483,2545,2611,2664,2722,2772,2833,2892,2960,3030,3099,3166,3225,3291,3356,3423,3478,3535,3612,3689", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,126,122,122,71,84,70,82,81,80,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "280,465,649,726,802,881,965,1053,1134,1200,1287,1376,1441,1503,1565,1631,1758,1881,2004,2076,2161,2232,2315,2397,2478,2540,2606,2659,2717,2767,2828,2887,2955,3025,3094,3161,3220,3286,3351,3418,3473,3530,3607,3684,3740"}, "to": {"startLines": "2,11,15,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,520,4568,4645,4721,4800,4884,4972,5053,5119,5206,5295,5360,5422,5484,5550,5677,5800,5923,5995,6080,6151,6234,6316,6397,6459,7196,7249,7307,7357,7418,7477,7545,7615,7684,7751,7810,7876,7941,8008,8063,8120,8197,8274", "endLines": "10,14,18,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,126,122,122,71,84,70,82,81,80,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "330,515,699,4640,4716,4795,4879,4967,5048,5114,5201,5290,5355,5417,5479,5545,5672,5795,5918,5990,6075,6146,6229,6311,6392,6454,6520,7244,7302,7352,7413,7472,7540,7610,7679,7746,7805,7871,7936,8003,8058,8115,8192,8269,8325"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d62d9a540e552a1187e018192472b047\\transformed\\material3-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,292,398,514,616,722,845,989,1117,1269,1360,1460,1560,1670,1794,1919,2024,2150,2276,2404,2566,2688,2802,2915,3038,3139,3239,3365,3504,3608,3713,3825,3950,4078,4195,4303,4379,4476,4572,4680,4768,4856,4957,5037,5121,5221,5323,5419,5528,5615,5720,5818,5929,6046,6126,6233", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "168,287,393,509,611,717,840,984,1112,1264,1355,1455,1555,1665,1789,1914,2019,2145,2271,2399,2561,2683,2797,2910,3033,3134,3234,3360,3499,3603,3708,3820,3945,4073,4190,4298,4374,4471,4567,4675,4763,4851,4952,5032,5116,5216,5318,5414,5523,5610,5715,5813,5924,6041,6121,6228,6328"}, "to": {"startLines": "111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8497,8615,8734,8840,8956,9058,9164,9287,9431,9559,9711,9802,9902,10002,10112,10236,10361,10466,10592,10718,10846,11008,11130,11244,11357,11480,11581,11681,11807,11946,12050,12155,12267,12392,12520,12637,12745,12821,12918,13014,13122,13210,13298,13399,13479,13563,13663,13765,13861,13970,14057,14162,14260,14371,14488,14568,14675", "endColumns": "117,118,105,115,101,105,122,143,127,151,90,99,99,109,123,124,104,125,125,127,161,121,113,112,122,100,99,125,138,103,104,111,124,127,116,107,75,96,95,107,87,87,100,79,83,99,101,95,108,86,104,97,110,116,79,106,99", "endOffsets": "8610,8729,8835,8951,9053,9159,9282,9426,9554,9706,9797,9897,9997,10107,10231,10356,10461,10587,10713,10841,11003,11125,11239,11352,11475,11576,11676,11802,11941,12045,12150,12262,12387,12515,12632,12740,12816,12913,13009,13117,13205,13293,13394,13474,13558,13658,13760,13856,13965,14052,14157,14255,14366,14483,14563,14670,14770"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\478b3be060432db7073f96b7c2278ef6\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,979,1064,1140,1214,1286,1357,1441,1507", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,974,1059,1135,1209,1281,1352,1436,1502,1620"}, "to": {"startLines": "53,54,55,56,57,109,110,168,169,170,171,173,174,175,176,178,179,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4113,4198,4278,4382,4480,8330,8414,14775,14860,14947,15027,15196,15272,15346,15418,15590,15674,15740", "endColumns": "84,79,103,97,87,83,82,84,86,79,84,75,73,71,70,83,65,117", "endOffsets": "4193,4273,4377,4475,4563,8409,8492,14855,14942,15022,15107,15267,15341,15413,15484,15669,15735,15853"}}]}]}