{"logs": [{"outputFile": "com.downloader.app-mergeReleaseResources-72:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f05148d81958cc6d8e30b6d34a1ab13\\transformed\\media3-exoplayer-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,263,335,413,493,583,676", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "131,193,258,330,408,488,578,671,745"}, "to": {"startLines": "84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6862,6943,7005,7070,7142,7220,7300,7390,7483", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "6938,7000,7065,7137,7215,7295,7385,7478,7552"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74d3d7c2d24a7100d6b0d87b145b1bf3\\transformed\\core-1.15.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "48,49,50,51,52,53,54,179", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3637,3735,3837,3937,4036,4138,4247,15944", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3730,3832,3932,4031,4133,4242,4359,16040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1328e28f1361bb606dde64f609a081c\\transformed\\media3-ui-1.2.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,940,1030,1119,1216,1310,1385,1451,1548,1646,1715,1778,1841,1910,2024,2137,2251,2328,2408,2477,2553,2652,2753,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3619,3685,3737,3799,3875,3951", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,935,1025,1114,1211,1305,1380,1446,1543,1641,1710,1773,1836,1905,2019,2132,2246,2323,2403,2472,2548,2647,2748,2814,2877,2930,2988,3036,3097,3161,3231,3296,3365,3426,3484,3550,3614,3680,3732,3794,3870,3946,4003"}, "to": {"startLines": "2,11,16,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,632,4832,4920,5010,5099,5196,5290,5365,5431,5528,5626,5695,5758,5821,5890,6004,6117,6231,6308,6388,6457,6533,6632,6733,6799,7557,7610,7668,7716,7777,7841,7911,7976,8045,8106,8164,8230,8294,8360,8412,8474,8550,8626", "endLines": "10,15,20,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "331,627,897,4915,5005,5094,5191,5285,5360,5426,5523,5621,5690,5753,5816,5885,5999,6112,6226,6303,6383,6452,6528,6627,6728,6794,6857,7605,7663,7711,7772,7836,7906,7971,8040,8101,8159,8225,8289,8355,8407,8469,8545,8621,8678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d62d9a540e552a1187e018192472b047\\transformed\\material3-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,312,429,555,665,762,876,1013,1133,1276,1360,1462,1557,1655,1775,1902,2009,2147,2283,2424,2600,2737,2856,2979,3105,3201,3297,3424,3565,3665,3770,3881,4021,4167,4279,4383,4459,4554,4646,4753,4839,4926,5027,5109,5192,5291,5395,5490,5591,5678,5789,5889,5995,6116,6198,6313", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "180,307,424,550,660,757,871,1008,1128,1271,1355,1457,1552,1650,1770,1897,2004,2142,2278,2419,2595,2732,2851,2974,3100,3196,3292,3419,3560,3660,3765,3876,4016,4162,4274,4378,4454,4549,4641,4748,4834,4921,5022,5104,5187,5286,5390,5485,5586,5673,5784,5884,5990,6111,6193,6308,6412"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8848,8978,9105,9222,9348,9458,9555,9669,9806,9926,10069,10153,10255,10350,10448,10568,10695,10802,10940,11076,11217,11393,11530,11649,11772,11898,11994,12090,12217,12358,12458,12563,12674,12814,12960,13072,13176,13252,13347,13439,13546,13632,13719,13820,13902,13985,14084,14188,14283,14384,14471,14582,14682,14788,14909,14991,15106", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "8973,9100,9217,9343,9453,9550,9664,9801,9921,10064,10148,10250,10345,10443,10563,10690,10797,10935,11071,11212,11388,11525,11644,11767,11893,11989,12085,12212,12353,12453,12558,12669,12809,12955,13067,13171,13247,13342,13434,13541,13627,13714,13815,13897,13980,14079,14183,14278,14379,14466,14577,14677,14783,14904,14986,15101,15205"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\478b3be060432db7073f96b7c2278ef6\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,999,1089,1162,1236,1315,1390,1467,1534", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,994,1084,1157,1231,1310,1385,1462,1529,1644"}, "to": {"startLines": "55,56,57,58,59,111,112,170,171,172,173,175,176,177,178,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4364,4461,4545,4642,4744,8683,8761,15210,15301,15383,15471,15643,15716,15790,15869,16045,16122,16189", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "4456,4540,4637,4739,4827,8756,8843,15296,15378,15466,15556,15711,15785,15864,15939,16117,16184,16299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28c5dc97a63a31061752728abbdc10f0\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "902,1025,1131,1244,1328,1433,1552,1637,1717,1808,1901,1996,2090,2190,2283,2378,2472,2563,2655,2736,2846,2954,3052,3164,3270,3374,3536,15561", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "1020,1126,1239,1323,1428,1547,1632,1712,1803,1896,1991,2085,2185,2278,2373,2467,2558,2650,2731,2841,2949,3047,3159,3265,3369,3531,3632,15638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7aa4dd35acc84f087f7df6becf4b1038\\transformed\\foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,150", "endColumns": "94,99", "endOffsets": "145,245"}, "to": {"startLines": "183,184", "startColumns": "4,4", "startOffsets": "16304,16399", "endColumns": "94,99", "endOffsets": "16394,16494"}}]}]}