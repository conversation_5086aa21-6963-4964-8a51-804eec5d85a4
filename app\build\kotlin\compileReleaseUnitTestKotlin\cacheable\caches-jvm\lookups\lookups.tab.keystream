  Context android.content  InstantTaskExecutorRule #androidx.arch.core.executor.testing  clearHistory androidx.lifecycle.ViewModel  clearMessage androidx.lifecycle.ViewModel  dismissClipboardDialog androidx.lifecycle.ViewModel  
startDownload androidx.lifecycle.ViewModel  	updateUrl androidx.lifecycle.ViewModel  TurbineTestContext app.cash.turbine  test app.cash.turbine  
assertThat #app.cash.turbine.TurbineTestContext  	awaitItem #app.cash.turbine.TurbineTestContext  
getASSERTThat #app.cash.turbine.TurbineTestContext  
getAssertThat #app.cash.turbine.TurbineTestContext  DownloadItem com.downloader.app.data.model  DownloadStatus com.downloader.app.data.model  PENDING ,com.downloader.app.data.model.DownloadStatus  Dispatchers com.downloader.app.service  ExperimentalCoroutinesApi com.downloader.app.service  InstagramExtractorService com.downloader.app.service  InstagramExtractorServiceTest com.downloader.app.service  InstantTaskExecutorRule com.downloader.app.service  OptIn com.downloader.app.service  StandardTestDispatcher com.downloader.app.service  String com.downloader.app.service  YouTubeExtractorService com.downloader.app.service  YouTubeExtractorServiceTest com.downloader.app.service  
assertThat com.downloader.app.service  
component1 com.downloader.app.service  
component2 com.downloader.app.service  forEach com.downloader.app.service  forEachIndexed com.downloader.app.service  instagramExtractorService com.downloader.app.service  invoke com.downloader.app.service  java com.downloader.app.service  listOf com.downloader.app.service  mapOf com.downloader.app.service  mockk com.downloader.app.service  	resetMain com.downloader.app.service  runTest com.downloader.app.service  setMain com.downloader.app.service  to com.downloader.app.service  youTubeExtractorService com.downloader.app.service  	Companion 4com.downloader.app.service.InstagramExtractorService  InstagramContentType 4com.downloader.app.service.InstagramExtractorService  extractInstagramUrl 4com.downloader.app.service.InstagramExtractorService  getInstagramContentType 4com.downloader.app.service.InstagramExtractorService  invoke >com.downloader.app.service.InstagramExtractorService.Companion  POST Icom.downloader.app.service.InstagramExtractorService.InstagramContentType  REEL Icom.downloader.app.service.InstagramExtractorService.InstagramContentType  STORY Icom.downloader.app.service.InstagramExtractorService.InstagramContentType  TV Icom.downloader.app.service.InstagramExtractorService.InstagramContentType  UNKNOWN Icom.downloader.app.service.InstagramExtractorService.InstagramContentType  After 8com.downloader.app.service.InstagramExtractorServiceTest  Before 8com.downloader.app.service.InstagramExtractorServiceTest  Context 8com.downloader.app.service.InstagramExtractorServiceTest  Dispatchers 8com.downloader.app.service.InstagramExtractorServiceTest  InstagramExtractorService 8com.downloader.app.service.InstagramExtractorServiceTest  InstantTaskExecutorRule 8com.downloader.app.service.InstagramExtractorServiceTest  Rule 8com.downloader.app.service.InstagramExtractorServiceTest  StandardTestDispatcher 8com.downloader.app.service.InstagramExtractorServiceTest  String 8com.downloader.app.service.InstagramExtractorServiceTest  Test 8com.downloader.app.service.InstagramExtractorServiceTest  
assertThat 8com.downloader.app.service.InstagramExtractorServiceTest  context 8com.downloader.app.service.InstagramExtractorServiceTest  forEachIndexed 8com.downloader.app.service.InstagramExtractorServiceTest  
getASSERTThat 8com.downloader.app.service.InstagramExtractorServiceTest  
getAssertThat 8com.downloader.app.service.InstagramExtractorServiceTest  getFOREachIndexed 8com.downloader.app.service.InstagramExtractorServiceTest  getForEachIndexed 8com.downloader.app.service.InstagramExtractorServiceTest  	getLISTOf 8com.downloader.app.service.InstagramExtractorServiceTest  	getListOf 8com.downloader.app.service.InstagramExtractorServiceTest  getMOCKK 8com.downloader.app.service.InstagramExtractorServiceTest  getMockk 8com.downloader.app.service.InstagramExtractorServiceTest  getRESETMain 8com.downloader.app.service.InstagramExtractorServiceTest  
getRUNTest 8com.downloader.app.service.InstagramExtractorServiceTest  getResetMain 8com.downloader.app.service.InstagramExtractorServiceTest  
getRunTest 8com.downloader.app.service.InstagramExtractorServiceTest  
getSETMain 8com.downloader.app.service.InstagramExtractorServiceTest  
getSetMain 8com.downloader.app.service.InstagramExtractorServiceTest  instagramExtractorService 8com.downloader.app.service.InstagramExtractorServiceTest  invoke 8com.downloader.app.service.InstagramExtractorServiceTest  java 8com.downloader.app.service.InstagramExtractorServiceTest  listOf 8com.downloader.app.service.InstagramExtractorServiceTest  mockk 8com.downloader.app.service.InstagramExtractorServiceTest  	resetMain 8com.downloader.app.service.InstagramExtractorServiceTest  runTest 8com.downloader.app.service.InstagramExtractorServiceTest  setMain 8com.downloader.app.service.InstagramExtractorServiceTest  testDispatcher 8com.downloader.app.service.InstagramExtractorServiceTest  	Companion 2com.downloader.app.service.YouTubeExtractorService  extractYouTubeUrl 2com.downloader.app.service.YouTubeExtractorService  invoke <com.downloader.app.service.YouTubeExtractorService.Companion  After 6com.downloader.app.service.YouTubeExtractorServiceTest  Before 6com.downloader.app.service.YouTubeExtractorServiceTest  Context 6com.downloader.app.service.YouTubeExtractorServiceTest  Dispatchers 6com.downloader.app.service.YouTubeExtractorServiceTest  InstantTaskExecutorRule 6com.downloader.app.service.YouTubeExtractorServiceTest  Rule 6com.downloader.app.service.YouTubeExtractorServiceTest  StandardTestDispatcher 6com.downloader.app.service.YouTubeExtractorServiceTest  String 6com.downloader.app.service.YouTubeExtractorServiceTest  Test 6com.downloader.app.service.YouTubeExtractorServiceTest  YouTubeExtractorService 6com.downloader.app.service.YouTubeExtractorServiceTest  
assertThat 6com.downloader.app.service.YouTubeExtractorServiceTest  
component1 6com.downloader.app.service.YouTubeExtractorServiceTest  
component2 6com.downloader.app.service.YouTubeExtractorServiceTest  context 6com.downloader.app.service.YouTubeExtractorServiceTest  
getASSERTThat 6com.downloader.app.service.YouTubeExtractorServiceTest  
getAssertThat 6com.downloader.app.service.YouTubeExtractorServiceTest  
getComponent1 6com.downloader.app.service.YouTubeExtractorServiceTest  
getComponent2 6com.downloader.app.service.YouTubeExtractorServiceTest  	getLISTOf 6com.downloader.app.service.YouTubeExtractorServiceTest  	getListOf 6com.downloader.app.service.YouTubeExtractorServiceTest  getMAPOf 6com.downloader.app.service.YouTubeExtractorServiceTest  getMOCKK 6com.downloader.app.service.YouTubeExtractorServiceTest  getMapOf 6com.downloader.app.service.YouTubeExtractorServiceTest  getMockk 6com.downloader.app.service.YouTubeExtractorServiceTest  getRESETMain 6com.downloader.app.service.YouTubeExtractorServiceTest  
getRUNTest 6com.downloader.app.service.YouTubeExtractorServiceTest  getResetMain 6com.downloader.app.service.YouTubeExtractorServiceTest  
getRunTest 6com.downloader.app.service.YouTubeExtractorServiceTest  
getSETMain 6com.downloader.app.service.YouTubeExtractorServiceTest  
getSetMain 6com.downloader.app.service.YouTubeExtractorServiceTest  getTO 6com.downloader.app.service.YouTubeExtractorServiceTest  getTo 6com.downloader.app.service.YouTubeExtractorServiceTest  invoke 6com.downloader.app.service.YouTubeExtractorServiceTest  java 6com.downloader.app.service.YouTubeExtractorServiceTest  listOf 6com.downloader.app.service.YouTubeExtractorServiceTest  mapOf 6com.downloader.app.service.YouTubeExtractorServiceTest  mockk 6com.downloader.app.service.YouTubeExtractorServiceTest  	resetMain 6com.downloader.app.service.YouTubeExtractorServiceTest  runTest 6com.downloader.app.service.YouTubeExtractorServiceTest  setMain 6com.downloader.app.service.YouTubeExtractorServiceTest  testDispatcher 6com.downloader.app.service.YouTubeExtractorServiceTest  to 6com.downloader.app.service.YouTubeExtractorServiceTest  youTubeExtractorService 6com.downloader.app.service.YouTubeExtractorServiceTest  Boolean com.downloader.app.ui.viewmodel  Date com.downloader.app.ui.viewmodel  Dispatchers com.downloader.app.ui.viewmodel  DownloadItem com.downloader.app.ui.viewmodel  DownloadStatus com.downloader.app.ui.viewmodel  DownloadUiState com.downloader.app.ui.viewmodel  DownloadViewModel com.downloader.app.ui.viewmodel  DownloadViewModelTest com.downloader.app.ui.viewmodel  	Exception com.downloader.app.ui.viewmodel  ExperimentalCoroutinesApi com.downloader.app.ui.viewmodel  InstantTaskExecutorRule com.downloader.app.ui.viewmodel  OptIn com.downloader.app.ui.viewmodel  Result com.downloader.app.ui.viewmodel  StandardTestDispatcher com.downloader.app.ui.viewmodel  String com.downloader.app.ui.viewmodel  UrlDetectionTest com.downloader.app.ui.viewmodel  
assertThat com.downloader.app.ui.viewmodel  clipboardHelper com.downloader.app.ui.viewmodel  coEvery com.downloader.app.ui.viewmodel  coVerify com.downloader.app.ui.viewmodel  
component1 com.downloader.app.ui.viewmodel  
component2 com.downloader.app.ui.viewmodel  every com.downloader.app.ui.viewmodel  flowOf com.downloader.app.ui.viewmodel  forEach com.downloader.app.ui.viewmodel  java com.downloader.app.ui.viewmodel  let com.downloader.app.ui.viewmodel  listOf com.downloader.app.ui.viewmodel  mapOf com.downloader.app.ui.viewmodel  mockk com.downloader.app.ui.viewmodel  	resetMain com.downloader.app.ui.viewmodel  runTest com.downloader.app.ui.viewmodel  setMain com.downloader.app.ui.viewmodel  test com.downloader.app.ui.viewmodel  testDispatcher com.downloader.app.ui.viewmodel  to com.downloader.app.ui.viewmodel  	viewModel com.downloader.app.ui.viewmodel  youTubeExtractorService com.downloader.app.ui.viewmodel  clipboardUrl /com.downloader.app.ui.viewmodel.DownloadUiState  copy /com.downloader.app.ui.viewmodel.DownloadUiState  
currentUrl /com.downloader.app.ui.viewmodel.DownloadUiState  errorMessage /com.downloader.app.ui.viewmodel.DownloadUiState  getLET /com.downloader.app.ui.viewmodel.DownloadUiState  getLet /com.downloader.app.ui.viewmodel.DownloadUiState  let /com.downloader.app.ui.viewmodel.DownloadUiState  showClipboardDialog /com.downloader.app.ui.viewmodel.DownloadUiState  successMessage /com.downloader.app.ui.viewmodel.DownloadUiState  clearHistory 1com.downloader.app.ui.viewmodel.DownloadViewModel  clearMessage 1com.downloader.app.ui.viewmodel.DownloadViewModel  dismissClipboardDialog 1com.downloader.app.ui.viewmodel.DownloadViewModel  	downloads 1com.downloader.app.ui.viewmodel.DownloadViewModel  
startDownload 1com.downloader.app.ui.viewmodel.DownloadViewModel  uiState 1com.downloader.app.ui.viewmodel.DownloadViewModel  	updateUrl 1com.downloader.app.ui.viewmodel.DownloadViewModel  After 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Before 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  ClipboardHelper 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Context 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Date 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Dispatchers 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  DownloadItem 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  DownloadStatus 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  DownloadViewModel 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  	Exception 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  InstagramExtractorService 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  InstantTaskExecutorRule 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Result 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Rule 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  StandardTestDispatcher 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  Test 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  YouTubeExtractorService 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
assertThat 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  clipboardHelper 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  coEvery 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  coVerify 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  context 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  every 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  flowOf 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getASSERTThat 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getAssertThat 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getCOEvery 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getCOVerify 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getCoEvery 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getCoVerify 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getEVERY 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getEvery 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  	getFLOWOf 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  	getFlowOf 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getLET 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getLet 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getMOCKK 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getMockk 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getRESETMain 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getRUNTest 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getResetMain 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getRunTest 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getSETMain 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  
getSetMain 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getTEST 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  getTest 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  instagramExtractorService 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  let 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  mockk 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  	resetMain 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  runTest 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  setMain 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  test 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  testDispatcher 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  	viewModel 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  youTubeExtractorService 5com.downloader.app.ui.viewmodel.DownloadViewModelTest  After 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Before 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Boolean 0com.downloader.app.ui.viewmodel.UrlDetectionTest  ClipboardHelper 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Context 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Dispatchers 0com.downloader.app.ui.viewmodel.UrlDetectionTest  DownloadViewModel 0com.downloader.app.ui.viewmodel.UrlDetectionTest  InstagramExtractorService 0com.downloader.app.ui.viewmodel.UrlDetectionTest  InstantTaskExecutorRule 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Rule 0com.downloader.app.ui.viewmodel.UrlDetectionTest  StandardTestDispatcher 0com.downloader.app.ui.viewmodel.UrlDetectionTest  String 0com.downloader.app.ui.viewmodel.UrlDetectionTest  Test 0com.downloader.app.ui.viewmodel.UrlDetectionTest  YouTubeExtractorService 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
assertThat 0com.downloader.app.ui.viewmodel.UrlDetectionTest  clipboardHelper 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
component1 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
component2 0com.downloader.app.ui.viewmodel.UrlDetectionTest  context 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
getASSERTThat 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
getAssertThat 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
getComponent1 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
getComponent2 0com.downloader.app.ui.viewmodel.UrlDetectionTest  	getLISTOf 0com.downloader.app.ui.viewmodel.UrlDetectionTest  	getListOf 0com.downloader.app.ui.viewmodel.UrlDetectionTest  getMAPOf 0com.downloader.app.ui.viewmodel.UrlDetectionTest  getMOCKK 0com.downloader.app.ui.viewmodel.UrlDetectionTest  getMapOf 0com.downloader.app.ui.viewmodel.UrlDetectionTest  getMockk 0com.downloader.app.ui.viewmodel.UrlDetectionTest  getRESETMain 0com.downloader.app.ui.viewmodel.UrlDetectionTest  getResetMain 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
getSETMain 0com.downloader.app.ui.viewmodel.UrlDetectionTest  
getSetMain 0com.downloader.app.ui.viewmodel.UrlDetectionTest  getTO 0com.downloader.app.ui.viewmodel.UrlDetectionTest  getTo 0com.downloader.app.ui.viewmodel.UrlDetectionTest  instagramExtractorService 0com.downloader.app.ui.viewmodel.UrlDetectionTest  java 0com.downloader.app.ui.viewmodel.UrlDetectionTest  listOf 0com.downloader.app.ui.viewmodel.UrlDetectionTest  mapOf 0com.downloader.app.ui.viewmodel.UrlDetectionTest  mockk 0com.downloader.app.ui.viewmodel.UrlDetectionTest  	resetMain 0com.downloader.app.ui.viewmodel.UrlDetectionTest  setMain 0com.downloader.app.ui.viewmodel.UrlDetectionTest  testDispatcher 0com.downloader.app.ui.viewmodel.UrlDetectionTest  to 0com.downloader.app.ui.viewmodel.UrlDetectionTest  	viewModel 0com.downloader.app.ui.viewmodel.UrlDetectionTest  youTubeExtractorService 0com.downloader.app.ui.viewmodel.UrlDetectionTest  ClipboardHelper com.downloader.app.utils  	FileUtils com.downloader.app.utils  
FileUtilsTest com.downloader.app.utils  
assertThat com.downloader.app.utils  monitorClipboard (com.downloader.app.utils.ClipboardHelper  getFileNameFromUrl "com.downloader.app.utils.FileUtils  getMimeTypeFromUrl "com.downloader.app.utils.FileUtils  
isValidUrl "com.downloader.app.utils.FileUtils  sanitizeFileName "com.downloader.app.utils.FileUtils  	FileUtils &com.downloader.app.utils.FileUtilsTest  Test &com.downloader.app.utils.FileUtilsTest  
assertThat &com.downloader.app.utils.FileUtilsTest  
getASSERTThat &com.downloader.app.utils.FileUtilsTest  
getAssertThat &com.downloader.app.utils.FileUtilsTest  BooleanSubject com.google.common.truth  ComparableSubject com.google.common.truth  IterableSubject com.google.common.truth  
StringSubject com.google.common.truth  Subject com.google.common.truth  Truth com.google.common.truth  isFalse &com.google.common.truth.BooleanSubject  isTrue &com.google.common.truth.BooleanSubject  contains )com.google.common.truth.ComparableSubject  isEmpty )com.google.common.truth.ComparableSubject  	isEqualTo )com.google.common.truth.ComparableSubject  
startsWith )com.google.common.truth.ComparableSubject  isEmpty 'com.google.common.truth.IterableSubject  contains %com.google.common.truth.StringSubject  isEmpty %com.google.common.truth.StringSubject  	isEqualTo %com.google.common.truth.StringSubject  
startsWith %com.google.common.truth.StringSubject  contains com.google.common.truth.Subject  isEmpty com.google.common.truth.Subject  	isEqualTo com.google.common.truth.Subject  isFalse com.google.common.truth.Subject  	isNotNull com.google.common.truth.Subject  isTrue com.google.common.truth.Subject  
startsWith com.google.common.truth.Subject  
assertThat com.google.common.truth.Truth  Date io.mockk  Dispatchers io.mockk  DownloadItem io.mockk  DownloadStatus io.mockk  DownloadViewModel io.mockk  	Exception io.mockk  ExperimentalCoroutinesApi io.mockk  InstantTaskExecutorRule io.mockk  MockKAdditionalAnswerScope io.mockk  MockKMatcherScope io.mockk  MockKStubScope io.mockk  MockKVerificationScope io.mockk  Result io.mockk  StandardTestDispatcher io.mockk  
assertThat io.mockk  clipboardHelper io.mockk  coEvery io.mockk  coVerify io.mockk  every io.mockk  flowOf io.mockk  let io.mockk  mockk io.mockk  	resetMain io.mockk  runTest io.mockk  setMain io.mockk  test io.mockk  testDispatcher io.mockk  	viewModel io.mockk  youTubeExtractorService io.mockk  clipboardHelper io.mockk.MockKMatcherScope  getCLIPBOARDHelper io.mockk.MockKMatcherScope  getClipboardHelper io.mockk.MockKMatcherScope  getYOUTubeExtractorService io.mockk.MockKMatcherScope  getYouTubeExtractorService io.mockk.MockKMatcherScope  youTubeExtractorService io.mockk.MockKMatcherScope  returns io.mockk.MockKStubScope  getYOUTubeExtractorService io.mockk.MockKVerificationScope  getYouTubeExtractorService io.mockk.MockKVerificationScope  youTubeExtractorService io.mockk.MockKVerificationScope  Class 	java.lang  Date 	java.lang  Dispatchers 	java.lang  DownloadItem 	java.lang  DownloadStatus 	java.lang  DownloadViewModel 	java.lang  	Exception 	java.lang  ExperimentalCoroutinesApi 	java.lang  	FileUtils 	java.lang  InstagramExtractorService 	java.lang  InstantTaskExecutorRule 	java.lang  Result 	java.lang  StandardTestDispatcher 	java.lang  String 	java.lang  YouTubeExtractorService 	java.lang  
assertThat 	java.lang  clipboardHelper 	java.lang  coEvery 	java.lang  coVerify 	java.lang  
component1 	java.lang  
component2 	java.lang  every 	java.lang  flowOf 	java.lang  forEach 	java.lang  forEachIndexed 	java.lang  instagramExtractorService 	java.lang  java 	java.lang  let 	java.lang  listOf 	java.lang  mapOf 	java.lang  mockk 	java.lang  	resetMain 	java.lang  runTest 	java.lang  setMain 	java.lang  test 	java.lang  testDispatcher 	java.lang  to 	java.lang  	viewModel 	java.lang  youTubeExtractorService 	java.lang  getDeclaredMethod java.lang.Class  Method java.lang.reflect  invoke "java.lang.reflect.AccessibleObject  invoke java.lang.reflect.Executable  getISAccessible java.lang.reflect.Method  getIsAccessible java.lang.reflect.Method  invoke java.lang.reflect.Method  isAccessible java.lang.reflect.Method  
setAccessible java.lang.reflect.Method  Date 	java.util  Any kotlin  Boolean kotlin  Date kotlin  Dispatchers kotlin  DownloadItem kotlin  DownloadStatus kotlin  DownloadViewModel kotlin  	Exception kotlin  ExperimentalCoroutinesApi kotlin  	FileUtils kotlin  	Function1 kotlin  	Function2 kotlin  InstagramExtractorService kotlin  InstantTaskExecutorRule kotlin  Int kotlin  OptIn kotlin  Pair kotlin  Result kotlin  StandardTestDispatcher kotlin  String kotlin  YouTubeExtractorService kotlin  
assertThat kotlin  clipboardHelper kotlin  coEvery kotlin  coVerify kotlin  
component1 kotlin  
component2 kotlin  every kotlin  flowOf kotlin  forEach kotlin  forEachIndexed kotlin  instagramExtractorService kotlin  java kotlin  let kotlin  listOf kotlin  mapOf kotlin  mockk kotlin  	resetMain kotlin  runTest kotlin  setMain kotlin  test kotlin  testDispatcher kotlin  to kotlin  	viewModel kotlin  youTubeExtractorService kotlin  exceptionOrNull 
kotlin.Result  failure 
kotlin.Result  	isFailure 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  	Companion 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  Date kotlin.annotation  Dispatchers kotlin.annotation  DownloadItem kotlin.annotation  DownloadStatus kotlin.annotation  DownloadViewModel kotlin.annotation  	Exception kotlin.annotation  ExperimentalCoroutinesApi kotlin.annotation  	FileUtils kotlin.annotation  InstagramExtractorService kotlin.annotation  InstantTaskExecutorRule kotlin.annotation  Result kotlin.annotation  StandardTestDispatcher kotlin.annotation  String kotlin.annotation  YouTubeExtractorService kotlin.annotation  
assertThat kotlin.annotation  clipboardHelper kotlin.annotation  coEvery kotlin.annotation  coVerify kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  every kotlin.annotation  flowOf kotlin.annotation  forEach kotlin.annotation  forEachIndexed kotlin.annotation  instagramExtractorService kotlin.annotation  java kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  mapOf kotlin.annotation  mockk kotlin.annotation  	resetMain kotlin.annotation  runTest kotlin.annotation  setMain kotlin.annotation  test kotlin.annotation  testDispatcher kotlin.annotation  to kotlin.annotation  	viewModel kotlin.annotation  youTubeExtractorService kotlin.annotation  Date kotlin.collections  Dispatchers kotlin.collections  DownloadItem kotlin.collections  DownloadStatus kotlin.collections  DownloadViewModel kotlin.collections  	Exception kotlin.collections  ExperimentalCoroutinesApi kotlin.collections  	FileUtils kotlin.collections  InstagramExtractorService kotlin.collections  InstantTaskExecutorRule kotlin.collections  List kotlin.collections  Map kotlin.collections  Result kotlin.collections  StandardTestDispatcher kotlin.collections  String kotlin.collections  YouTubeExtractorService kotlin.collections  
assertThat kotlin.collections  clipboardHelper kotlin.collections  coEvery kotlin.collections  coVerify kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  every kotlin.collections  flowOf kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  instagramExtractorService kotlin.collections  java kotlin.collections  let kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  mockk kotlin.collections  	resetMain kotlin.collections  runTest kotlin.collections  setMain kotlin.collections  test kotlin.collections  testDispatcher kotlin.collections  to kotlin.collections  	viewModel kotlin.collections  youTubeExtractorService kotlin.collections  getFOREachIndexed kotlin.collections.List  getForEachIndexed kotlin.collections.List  Entry kotlin.collections.Map  
getComponent1 kotlin.collections.Map.Entry  
getComponent2 kotlin.collections.Map.Entry  Date kotlin.comparisons  Dispatchers kotlin.comparisons  DownloadItem kotlin.comparisons  DownloadStatus kotlin.comparisons  DownloadViewModel kotlin.comparisons  	Exception kotlin.comparisons  ExperimentalCoroutinesApi kotlin.comparisons  	FileUtils kotlin.comparisons  InstagramExtractorService kotlin.comparisons  InstantTaskExecutorRule kotlin.comparisons  Result kotlin.comparisons  StandardTestDispatcher kotlin.comparisons  String kotlin.comparisons  YouTubeExtractorService kotlin.comparisons  
assertThat kotlin.comparisons  clipboardHelper kotlin.comparisons  coEvery kotlin.comparisons  coVerify kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  every kotlin.comparisons  flowOf kotlin.comparisons  forEach kotlin.comparisons  forEachIndexed kotlin.comparisons  instagramExtractorService kotlin.comparisons  java kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  mapOf kotlin.comparisons  mockk kotlin.comparisons  	resetMain kotlin.comparisons  runTest kotlin.comparisons  setMain kotlin.comparisons  test kotlin.comparisons  testDispatcher kotlin.comparisons  to kotlin.comparisons  	viewModel kotlin.comparisons  youTubeExtractorService kotlin.comparisons  SuspendFunction1 kotlin.coroutines  advanceUntilIdle 1kotlin.coroutines.AbstractCoroutineContextElement  Date 	kotlin.io  Dispatchers 	kotlin.io  DownloadItem 	kotlin.io  DownloadStatus 	kotlin.io  DownloadViewModel 	kotlin.io  	Exception 	kotlin.io  ExperimentalCoroutinesApi 	kotlin.io  	FileUtils 	kotlin.io  InstagramExtractorService 	kotlin.io  InstantTaskExecutorRule 	kotlin.io  Result 	kotlin.io  StandardTestDispatcher 	kotlin.io  String 	kotlin.io  YouTubeExtractorService 	kotlin.io  
assertThat 	kotlin.io  clipboardHelper 	kotlin.io  coEvery 	kotlin.io  coVerify 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  every 	kotlin.io  flowOf 	kotlin.io  forEach 	kotlin.io  forEachIndexed 	kotlin.io  instagramExtractorService 	kotlin.io  java 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  mapOf 	kotlin.io  mockk 	kotlin.io  	resetMain 	kotlin.io  runTest 	kotlin.io  setMain 	kotlin.io  test 	kotlin.io  testDispatcher 	kotlin.io  to 	kotlin.io  	viewModel 	kotlin.io  youTubeExtractorService 	kotlin.io  Date 
kotlin.jvm  Dispatchers 
kotlin.jvm  DownloadItem 
kotlin.jvm  DownloadStatus 
kotlin.jvm  DownloadViewModel 
kotlin.jvm  	Exception 
kotlin.jvm  ExperimentalCoroutinesApi 
kotlin.jvm  	FileUtils 
kotlin.jvm  InstagramExtractorService 
kotlin.jvm  InstantTaskExecutorRule 
kotlin.jvm  Result 
kotlin.jvm  StandardTestDispatcher 
kotlin.jvm  String 
kotlin.jvm  YouTubeExtractorService 
kotlin.jvm  
assertThat 
kotlin.jvm  clipboardHelper 
kotlin.jvm  coEvery 
kotlin.jvm  coVerify 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  every 
kotlin.jvm  flowOf 
kotlin.jvm  forEach 
kotlin.jvm  forEachIndexed 
kotlin.jvm  instagramExtractorService 
kotlin.jvm  java 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  mapOf 
kotlin.jvm  mockk 
kotlin.jvm  	resetMain 
kotlin.jvm  runTest 
kotlin.jvm  setMain 
kotlin.jvm  test 
kotlin.jvm  testDispatcher 
kotlin.jvm  to 
kotlin.jvm  	viewModel 
kotlin.jvm  youTubeExtractorService 
kotlin.jvm  Date 
kotlin.ranges  Dispatchers 
kotlin.ranges  DownloadItem 
kotlin.ranges  DownloadStatus 
kotlin.ranges  DownloadViewModel 
kotlin.ranges  	Exception 
kotlin.ranges  ExperimentalCoroutinesApi 
kotlin.ranges  	FileUtils 
kotlin.ranges  InstagramExtractorService 
kotlin.ranges  InstantTaskExecutorRule 
kotlin.ranges  Result 
kotlin.ranges  StandardTestDispatcher 
kotlin.ranges  String 
kotlin.ranges  YouTubeExtractorService 
kotlin.ranges  
assertThat 
kotlin.ranges  clipboardHelper 
kotlin.ranges  coEvery 
kotlin.ranges  coVerify 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  every 
kotlin.ranges  flowOf 
kotlin.ranges  forEach 
kotlin.ranges  forEachIndexed 
kotlin.ranges  instagramExtractorService 
kotlin.ranges  java 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  mapOf 
kotlin.ranges  mockk 
kotlin.ranges  	resetMain 
kotlin.ranges  runTest 
kotlin.ranges  setMain 
kotlin.ranges  test 
kotlin.ranges  testDispatcher 
kotlin.ranges  to 
kotlin.ranges  	viewModel 
kotlin.ranges  youTubeExtractorService 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  Date kotlin.sequences  Dispatchers kotlin.sequences  DownloadItem kotlin.sequences  DownloadStatus kotlin.sequences  DownloadViewModel kotlin.sequences  	Exception kotlin.sequences  ExperimentalCoroutinesApi kotlin.sequences  	FileUtils kotlin.sequences  InstagramExtractorService kotlin.sequences  InstantTaskExecutorRule kotlin.sequences  Result kotlin.sequences  StandardTestDispatcher kotlin.sequences  String kotlin.sequences  YouTubeExtractorService kotlin.sequences  
assertThat kotlin.sequences  clipboardHelper kotlin.sequences  coEvery kotlin.sequences  coVerify kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  every kotlin.sequences  flowOf kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  instagramExtractorService kotlin.sequences  java kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  mapOf kotlin.sequences  mockk kotlin.sequences  	resetMain kotlin.sequences  runTest kotlin.sequences  setMain kotlin.sequences  test kotlin.sequences  testDispatcher kotlin.sequences  to kotlin.sequences  	viewModel kotlin.sequences  youTubeExtractorService kotlin.sequences  Date kotlin.text  Dispatchers kotlin.text  DownloadItem kotlin.text  DownloadStatus kotlin.text  DownloadViewModel kotlin.text  	Exception kotlin.text  ExperimentalCoroutinesApi kotlin.text  	FileUtils kotlin.text  InstagramExtractorService kotlin.text  InstantTaskExecutorRule kotlin.text  Result kotlin.text  StandardTestDispatcher kotlin.text  String kotlin.text  YouTubeExtractorService kotlin.text  
assertThat kotlin.text  clipboardHelper kotlin.text  coEvery kotlin.text  coVerify kotlin.text  
component1 kotlin.text  
component2 kotlin.text  every kotlin.text  flowOf kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  instagramExtractorService kotlin.text  java kotlin.text  let kotlin.text  listOf kotlin.text  mapOf kotlin.text  mockk kotlin.text  	resetMain kotlin.text  runTest kotlin.text  setMain kotlin.text  test kotlin.text  testDispatcher kotlin.text  to kotlin.text  	viewModel kotlin.text  youTubeExtractorService kotlin.text  Dispatchers kotlinx.coroutines  ExperimentalCoroutinesApi kotlinx.coroutines  getRESETMain kotlinx.coroutines.Dispatchers  getResetMain kotlinx.coroutines.Dispatchers  
getSETMain kotlinx.coroutines.Dispatchers  
getSetMain kotlinx.coroutines.Dispatchers  	resetMain kotlinx.coroutines.Dispatchers  setMain kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  flowOf kotlinx.coroutines.flow  getTEST (kotlinx.coroutines.flow.MutableStateFlow  getTest (kotlinx.coroutines.flow.MutableStateFlow  test (kotlinx.coroutines.flow.MutableStateFlow  getTEST !kotlinx.coroutines.flow.StateFlow  getTest !kotlinx.coroutines.flow.StateFlow  test !kotlinx.coroutines.flow.StateFlow  value !kotlinx.coroutines.flow.StateFlow  Date kotlinx.coroutines.test  Dispatchers kotlinx.coroutines.test  DownloadItem kotlinx.coroutines.test  DownloadStatus kotlinx.coroutines.test  DownloadViewModel kotlinx.coroutines.test  	Exception kotlinx.coroutines.test  ExperimentalCoroutinesApi kotlinx.coroutines.test  InstagramExtractorService kotlinx.coroutines.test  InstantTaskExecutorRule kotlinx.coroutines.test  Result kotlinx.coroutines.test  StandardTestDispatcher kotlinx.coroutines.test  String kotlinx.coroutines.test  TestDispatcher kotlinx.coroutines.test  	TestScope kotlinx.coroutines.test  YouTubeExtractorService kotlinx.coroutines.test  
assertThat kotlinx.coroutines.test  clipboardHelper kotlinx.coroutines.test  coEvery kotlinx.coroutines.test  coVerify kotlinx.coroutines.test  
component1 kotlinx.coroutines.test  
component2 kotlinx.coroutines.test  every kotlinx.coroutines.test  flowOf kotlinx.coroutines.test  forEach kotlinx.coroutines.test  forEachIndexed kotlinx.coroutines.test  instagramExtractorService kotlinx.coroutines.test  java kotlinx.coroutines.test  let kotlinx.coroutines.test  listOf kotlinx.coroutines.test  mapOf kotlinx.coroutines.test  mockk kotlinx.coroutines.test  	resetMain kotlinx.coroutines.test  runTest kotlinx.coroutines.test  setMain kotlinx.coroutines.test  test kotlinx.coroutines.test  testDispatcher kotlinx.coroutines.test  to kotlinx.coroutines.test  	viewModel kotlinx.coroutines.test  youTubeExtractorService kotlinx.coroutines.test  advanceUntilIdle .kotlinx.coroutines.test.TestCoroutineScheduler  	scheduler &kotlinx.coroutines.test.TestDispatcher  Date !kotlinx.coroutines.test.TestScope  DownloadItem !kotlinx.coroutines.test.TestScope  DownloadStatus !kotlinx.coroutines.test.TestScope  	Exception !kotlinx.coroutines.test.TestScope  Result !kotlinx.coroutines.test.TestScope  
assertThat !kotlinx.coroutines.test.TestScope  coEvery !kotlinx.coroutines.test.TestScope  coVerify !kotlinx.coroutines.test.TestScope  
getASSERTThat !kotlinx.coroutines.test.TestScope  
getAssertThat !kotlinx.coroutines.test.TestScope  
getCOEvery !kotlinx.coroutines.test.TestScope  getCOVerify !kotlinx.coroutines.test.TestScope  
getCoEvery !kotlinx.coroutines.test.TestScope  getCoVerify !kotlinx.coroutines.test.TestScope  getINSTAGRAMExtractorService !kotlinx.coroutines.test.TestScope  getInstagramExtractorService !kotlinx.coroutines.test.TestScope  getLET !kotlinx.coroutines.test.TestScope  	getLISTOf !kotlinx.coroutines.test.TestScope  getLet !kotlinx.coroutines.test.TestScope  	getListOf !kotlinx.coroutines.test.TestScope  getTEST !kotlinx.coroutines.test.TestScope  getTESTDispatcher !kotlinx.coroutines.test.TestScope  getTest !kotlinx.coroutines.test.TestScope  getTestDispatcher !kotlinx.coroutines.test.TestScope  getVIEWModel !kotlinx.coroutines.test.TestScope  getViewModel !kotlinx.coroutines.test.TestScope  getYOUTubeExtractorService !kotlinx.coroutines.test.TestScope  getYouTubeExtractorService !kotlinx.coroutines.test.TestScope  instagramExtractorService !kotlinx.coroutines.test.TestScope  let !kotlinx.coroutines.test.TestScope  listOf !kotlinx.coroutines.test.TestScope  test !kotlinx.coroutines.test.TestScope  testDispatcher !kotlinx.coroutines.test.TestScope  	viewModel !kotlinx.coroutines.test.TestScope  youTubeExtractorService !kotlinx.coroutines.test.TestScope  After 	org.junit  Before 	org.junit  Rule 	org.junit  Test 	org.junit                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        