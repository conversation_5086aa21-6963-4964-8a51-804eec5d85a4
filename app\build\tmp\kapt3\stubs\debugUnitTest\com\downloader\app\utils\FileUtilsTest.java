package com.downloader.app.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0011\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0007J\b\u0010\u0005\u001a\u00020\u0004H\u0007J\b\u0010\u0006\u001a\u00020\u0004H\u0007J\b\u0010\u0007\u001a\u00020\u0004H\u0007J\b\u0010\b\u001a\u00020\u0004H\u0007J\b\u0010\t\u001a\u00020\u0004H\u0007J\b\u0010\n\u001a\u00020\u0004H\u0007J\b\u0010\u000b\u001a\u00020\u0004H\u0007J\b\u0010\f\u001a\u00020\u0004H\u0007J\b\u0010\r\u001a\u00020\u0004H\u0007J\b\u0010\u000e\u001a\u00020\u0004H\u0007J\b\u0010\u000f\u001a\u00020\u0004H\u0007J\b\u0010\u0010\u001a\u00020\u0004H\u0007J\b\u0010\u0011\u001a\u00020\u0004H\u0007J\b\u0010\u0012\u001a\u00020\u0004H\u0007J\b\u0010\u0013\u001a\u00020\u0004H\u0007J\b\u0010\u0014\u001a\u00020\u0004H\u0007\u00a8\u0006\u0015"}, d2 = {"Lcom/downloader/app/utils/FileUtilsTest;", "", "()V", "getFileNameFromUrl should extract filename from valid URL", "", "getFileNameFromUrl should generate default name for URL without extension", "getFileNameFromUrl should generate default name for URL without filename", "getFileNameFromUrl should handle URL with fragment", "getFileNameFromUrl should handle URL with query parameters", "getMimeTypeFromUrl should return a mime type for audio", "getMimeTypeFromUrl should return a mime type for image", "getMimeTypeFromUrl should return a mime type for video", "getMimeTypeFromUrl should return default mime type for unknown extension", "isValidUrl should return false for empty string", "isValidUrl should return false for invalid URL", "isValidUrl should return true for URL without protocol", "isValidUrl should return true for valid HTTP URL", "isValidUrl should return true for valid HTTPS URL", "sanitizeFileName should handle multiple consecutive underscores", "sanitizeFileName should replace invalid characters", "sanitizeFileName should trim leading and trailing underscores", "app_debugUnitTest"})
public final class FileUtilsTest {
    
    public FileUtilsTest() {
        super();
    }
}