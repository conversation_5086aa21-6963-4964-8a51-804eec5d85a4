package com.downloader.app.data.database;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0007\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH'J\u0018\u0010\u0010\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u001c\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00070\u000f2\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\u0015\u001a\u00020\u00122\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\u0016\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ&\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0018\u001a\u00020\u0019H\u00a7@\u00a2\u0006\u0002\u0010\u001aJ6\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u001f\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010 J(\u0010!\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\n\u001a\u00020\u000b2\b\u0010\"\u001a\u0004\u0018\u00010\u0012H\u00a7@\u00a2\u0006\u0002\u0010#\u00a8\u0006$"}, d2 = {"Lcom/downloader/app/data/database/DownloadDao;", "", "clearAllDownloads", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteDownload", "download", "Lcom/downloader/app/data/model/DownloadItem;", "(Lcom/downloader/app/data/model/DownloadItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteDownloadsByStatus", "status", "Lcom/downloader/app/data/model/DownloadStatus;", "(Lcom/downloader/app/data/model/DownloadStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllDownloads", "Lkotlinx/coroutines/flow/Flow;", "", "getDownloadById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getDownloadsByStatus", "insertDownload", "updateDownload", "updateDownloadError", "errorMessage", "", "(JLcom/downloader/app/data/model/DownloadStatus;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDownloadProgress", "progress", "", "downloadedSize", "speed", "(JLcom/downloader/app/data/model/DownloadStatus;IJJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDownloadStatus", "completedAt", "(JLcom/downloader/app/data/model/DownloadStatus;Ljava/lang/Long;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface DownloadDao {
    
    @androidx.room.Query(value = "SELECT * FROM DownloadItem")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.downloader.app.data.model.DownloadItem>> getAllDownloads();
    
    @androidx.room.Query(value = "SELECT * FROM DownloadItem WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDownloadById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.downloader.app.data.model.DownloadItem> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM DownloadItem WHERE status = :status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDownloadsByStatus(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.downloader.app.data.model.DownloadItem>> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertDownload(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem download, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDownload(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem download, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE DownloadItem SET status = :status, progress = :progress, downloadedSize = :downloadedSize, downloadSpeed = :speed WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDownloadProgress(long id, @org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, int progress, long downloadedSize, long speed, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE DownloadItem SET status = :status, completedAt = :completedAt WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDownloadStatus(long id, @org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, @org.jetbrains.annotations.Nullable()
    java.lang.Long completedAt, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE DownloadItem SET status = :status, errorMessage = :errorMessage WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDownloadError(long id, @org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteDownload(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem download, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM DownloadItem")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearAllDownloads(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM DownloadItem WHERE status = :status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteDownloadsByStatus(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}