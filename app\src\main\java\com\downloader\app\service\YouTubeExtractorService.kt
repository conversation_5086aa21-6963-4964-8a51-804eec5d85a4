package com.downloader.app.service

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.util.SparseArray
import at.huber.youtubeExtractor.VideoMeta
import at.huber.youtubeExtractor.YouTubeExtractor
import at.huber.youtubeExtractor.YtFile
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.data.model.DownloadStatus
import com.downloader.app.data.model.FileType
import com.downloader.app.utils.FileUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.Date
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class YouTubeExtractorService @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "YouTubeExtractor"
    }

    suspend fun extractYouTubeUrl(youtubeUrl: String): Result<DownloadItem> = withContext(Dispatchers.IO) {
        try {
            val normalizedUrl = normalizeYouTubeUrl(youtubeUrl)
            Log.d(TAG, "Extracting from normalized URL: $normalizedUrl")
            
            var result: Result<DownloadItem>? = null
            val latch = CountDownLatch(1)

            Handler(Looper.getMainLooper()).post {
                val extractor = object : YouTubeExtractor(context) {
                    override fun onExtractionComplete(ytFiles: SparseArray<YtFile>?, videoMeta: VideoMeta?) {
                        try {
                            if (ytFiles == null || ytFiles.size() == 0) {
                                result = Result.failure(Exception("No YouTube streams found"))
                                latch.countDown()
                                return
                            }

                            // Get the best video quality with audio
                            var bestQualityVideoWithAudio: YtFile? = null
                            var highestVideoQuality = 0

                            for (i in 0 until ytFiles.size()) {
                                val format = ytFiles.keyAt(i)
                                val ytFile = ytFiles.get(format)

                                // Check if this format has both video and audio
                                if (ytFile.format.audioBitrate > 0 && ytFile.format.height > 0) {
                                    if (ytFile.format.height > highestVideoQuality) {
                                        highestVideoQuality = ytFile.format.height
                                        bestQualityVideoWithAudio = ytFile
                                    }
                                }
                            }

                            // If no video with audio found, try to get best audio only
                            if (bestQualityVideoWithAudio == null) {
                                var bestAudioBitrate = 0
                                for (i in 0 until ytFiles.size()) {
                                    val format = ytFiles.keyAt(i)
                                    val ytFile = ytFiles.get(format)
                                    if (ytFile.format.audioBitrate > bestAudioBitrate) {
                                        bestAudioBitrate = ytFile.format.audioBitrate
                                        bestQualityVideoWithAudio = ytFile
                                    }
                                }
                            }

                            if (bestQualityVideoWithAudio != null) {
                                val downloadUrl = bestQualityVideoWithAudio.url
                                val title = videoMeta?.title ?: "youtube_video"
                                val sanitizedTitle = title.replace("[^a-zA-Z0-9.-]".toRegex(), "_")
                                val extension = bestQualityVideoWithAudio.format.ext
                                val safeExtension = if (extension.isBlank()) "mp4" else extension
                                
                                val fileName = "$sanitizedTitle.$safeExtension"
                                
                                val fileType = if (bestQualityVideoWithAudio.format.height <= 0) {
                                    FileType.AUDIO
                                } else {
                                    FileType.VIDEO
                                }
                                
                                val downloadDir = FileUtils.getDownloadDirectory(fileType)
                                val uniqueFileName = FileUtils.getUniqueFileName(downloadDir, fileName)
                                val filePath = File(downloadDir, uniqueFileName).absolutePath

                                val mimeType = if (fileType == FileType.AUDIO) {
                                    "audio/$safeExtension"
                                } else {
                                    "video/$safeExtension"
                                }
                                
                                val downloadItem = DownloadItem(
                                    url = downloadUrl,
                                    fileName = uniqueFileName,
                                    filePath = filePath,
                                    mimeType = mimeType,
                                    status = DownloadStatus.PENDING,
                                    createdAt = Date(),
                                    originalUrl = youtubeUrl
                                )

                                result = Result.success(downloadItem)
                            } else {
                                result = Result.failure(Exception("No suitable YouTube format found"))
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error processing YouTube streams", e)
                            result = Result.failure(e)
                        }
                        
                        latch.countDown()
                    }
                }
                extractor.extract(normalizedUrl, true, true)
            }

            if (!latch.await(30, TimeUnit.SECONDS)) {
                Log.e(TAG, "YouTube extraction timed out after 30 seconds")
                return@withContext Result.failure(Exception("YouTube extraction timed out"))
            }
            
            return@withContext result ?: Result.failure(Exception("YouTube extraction failed"))
        } catch (e: Exception) {
            Log.e(TAG, "YouTube extraction error", e)
            return@withContext Result.failure(e)
        }
    }
    
    private fun normalizeYouTubeUrl(url: String): String {
        // Handle youtu.be short URLs
        if (url.contains("youtu.be")) {
            val videoId = url.substringAfter("youtu.be/").substringBefore("?").substringBefore("&")
            return "https://www.youtube.com/watch?v=$videoId"
        }
        
        // Handle mobile URLs (m.youtube.com)
        if (url.contains("m.youtube.com")) {
            return url.replace("m.youtube.com", "www.youtube.com")
        }
        
        // Handle YouTube Music URLs
        if (url.contains("music.youtube.com")) {
            val videoId = url.substringAfter("v=").substringBefore("&")
            if (videoId.isNotEmpty()) {
                return "https://www.youtube.com/watch?v=$videoId"
            }
        }
        
        // Handle YouTube Shorts
        if (url.contains("/shorts/")) {
            val videoId = url.substringAfter("/shorts/").substringBefore("?").substringBefore("&")
            return "https://www.youtube.com/watch?v=$videoId"
        }
        
        return url
    }
}
