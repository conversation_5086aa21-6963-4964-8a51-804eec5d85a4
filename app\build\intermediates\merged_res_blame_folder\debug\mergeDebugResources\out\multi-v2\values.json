{"logs": [{"outputFile": "com.downloader.app-mergeDebugResources-76:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\rv\\Downloader\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "293,57,105,153,201,247,336", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "330,99,147,195,241,287,373"}, "to": {"startLines": "103,155,156,157,168,169,172", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5019,8482,8529,8576,9320,9365,9531", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "5056,8524,8571,8618,9360,9405,9568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b1328e28f1361bb606dde64f609a081c\\transformed\\media3-ui-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,29,30,31,32,33,34,35,40,47,48,49,50,51,52,57,58,59,60,61,62,63,64,65,66,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,213,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,266,270,274,278,282,286,290,294,295,301,312,316,320,324,328,332,336,340,344,348,352,356,367,372,377,382,393,401,411,415,419,423,426,442,468,497", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,385,439,493,656,702,751,877,926,975,1034,1088,1140,1190,1255,1312,1359,1414,1562,1800,1849,1910,1970,2026,2086,2256,2316,2369,2426,2481,2537,2594,2643,2694,2753,3040,3105,3163,3212,3260,3311,3368,3425,3487,3554,3625,3697,3741,3798,3854,3917,3990,4060,4119,4176,4223,4278,4323,4372,4427,4481,4531,4582,4636,4695,4745,4803,4859,4912,4975,5040,5103,5155,5215,5279,5345,5403,5475,5536,5606,5676,5741,5806,5877,5972,6077,6180,6261,6344,6425,6514,6607,6700,6793,6878,6973,7066,7143,7235,7313,7393,7471,7557,7639,7732,7810,7901,7982,8071,8174,8275,8359,8455,8552,8647,8740,8832,8925,9018,9111,9194,9281,9376,9469,9550,9645,9738,9815,9859,9900,9945,9993,10037,10080,10129,10176,10220,10276,10329,10371,10418,10466,10526,10564,10614,10658,10708,10760,10798,10845,10892,10933,10972,11010,11054,11102,11144,11182,11224,11278,11325,11362,11411,11453,11494,11535,11577,11620,11658,11694,11772,11850,12147,12417,12499,12581,12723,12801,12888,12973,13040,13103,13195,13287,13352,13415,13477,13548,13658,13769,13879,13946,14026,14097,14164,14249,14334,14397,14485,14549,14691,14791,14839,14982,15045,15107,15172,15243,15301,15359,15425,15489,15555,15607,15669,15745,15821,15875,16154,16385,16595,16808,17018,17240,17456,17660,17698,18052,18839,19080,19320,19577,19830,20083,20318,20565,20804,21048,21269,21464,22039,22330,22626,22929,23498,24032,24506,24717,24917,25093,25201,25777,26722,27772", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,29,30,31,32,33,34,39,46,47,48,49,50,51,56,57,58,59,60,61,62,63,64,65,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,212,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,265,269,273,277,281,285,289,293,294,300,311,315,319,323,327,331,335,339,343,347,351,355,366,371,376,381,392,400,410,414,418,422,425,441,467,496,536", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "330,380,434,488,651,697,746,872,921,970,1029,1083,1135,1185,1250,1307,1354,1409,1557,1795,1844,1905,1965,2021,2081,2251,2311,2364,2421,2476,2532,2589,2638,2689,2748,3035,3100,3158,3207,3255,3306,3363,3420,3482,3549,3620,3692,3736,3793,3849,3912,3985,4055,4114,4171,4218,4273,4318,4367,4422,4476,4526,4577,4631,4690,4740,4798,4854,4907,4970,5035,5098,5150,5210,5274,5340,5398,5470,5531,5601,5671,5736,5801,5872,5967,6072,6175,6256,6339,6420,6509,6602,6695,6788,6873,6968,7061,7138,7230,7308,7388,7466,7552,7634,7727,7805,7896,7977,8066,8169,8270,8354,8450,8547,8642,8735,8827,8920,9013,9106,9189,9276,9371,9464,9545,9640,9733,9810,9854,9895,9940,9988,10032,10075,10124,10171,10215,10271,10324,10366,10413,10461,10521,10559,10609,10653,10703,10755,10793,10840,10887,10928,10967,11005,11049,11097,11139,11177,11219,11273,11320,11357,11406,11448,11489,11530,11572,11615,11653,11689,11767,11845,12142,12412,12494,12576,12718,12796,12883,12968,13035,13098,13190,13282,13347,13410,13472,13543,13653,13764,13874,13941,14021,14092,14159,14244,14329,14392,14480,14544,14686,14786,14834,14977,15040,15102,15167,15238,15296,15354,15420,15484,15550,15602,15664,15740,15816,15870,16149,16380,16590,16803,17013,17235,17451,17655,17693,18047,18834,19075,19315,19572,19825,20078,20313,20560,20799,21043,21264,21459,22034,22325,22621,22924,23493,24027,24501,24712,24912,25088,25196,25772,26717,27767,29125"}, "to": {"startLines": "2,11,12,13,14,19,20,21,25,26,27,28,31,32,34,36,37,38,40,45,52,53,54,55,56,57,62,63,64,65,66,67,68,69,70,71,78,80,81,82,83,120,121,122,123,124,125,126,127,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,507,508,511,515,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,1846,1851,1855,1859,1863,1867,1871,1875,1879,1880,1886,1897,1901,1905,1909,1913,1917,1921,1925,1929,1933,1937,1941,1952,1957,1962,1967,1978,1986,1996,2000,2004,3098,3177,3315,3581,3610", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,430,484,538,701,747,796,922,971,1020,1079,1233,1285,1384,1509,1566,1613,1724,1872,2110,2159,2220,2280,2336,2396,2566,2626,2679,2736,2791,2847,2904,2953,3004,3063,3350,3456,3514,3563,3611,6234,6291,6348,6410,6477,6548,6620,6664,15683,15739,15802,15875,15945,16004,16061,16108,16163,16208,16257,16312,16366,16416,16467,16521,16580,16630,16688,16744,16797,16860,16925,16988,17040,17100,17164,17230,17288,17360,17421,17491,17561,17626,17691,20190,20285,20390,20493,20574,20657,20738,20827,20920,21013,21106,21191,21286,21379,21456,21548,21626,21706,21784,21870,21952,22045,22123,22214,22295,22384,22487,22588,22672,22768,22865,22960,23053,23145,23238,23331,23424,23507,23594,23689,23782,23863,23958,24051,26693,26737,26778,26823,26871,26915,26958,27007,27054,27098,27154,27207,27249,27296,27344,27404,27442,27492,27536,27586,27638,27676,27723,27770,27811,27850,27888,27932,27980,28022,28060,28102,28156,28203,28240,28289,28331,28372,28413,28455,28498,28536,30914,30992,31213,31510,35220,35302,35384,35526,35604,35691,35776,35843,35906,35998,36090,36155,36218,36280,36351,36461,36572,36682,36749,36829,36900,36967,37052,37137,37200,37288,37998,38140,38240,38288,38431,38494,38556,38621,38692,38750,38808,38874,38938,39004,39056,39118,39194,39270,118153,118432,118663,118873,119086,119296,119518,119734,119938,119976,120330,121117,121358,121598,121855,122108,122361,122596,122843,123082,123326,123547,123742,124317,124608,124904,125207,125776,126310,126784,126995,127195,182604,186078,190881,199977,201027", "endLines": "10,11,12,13,18,19,20,24,25,26,27,28,31,32,34,36,37,38,44,51,52,53,54,55,56,61,62,63,64,65,66,67,68,69,70,77,78,80,81,82,83,120,121,122,123,124,125,126,127,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,461,462,507,508,514,518,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,594,595,605,606,607,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,1850,1854,1858,1862,1866,1870,1874,1878,1879,1885,1896,1900,1904,1908,1912,1916,1920,1924,1928,1932,1936,1940,1951,1956,1961,1966,1977,1985,1995,1999,2003,2007,3100,3192,3340,3609,3649", "endColumns": "17,49,53,53,9,45,48,9,48,48,58,53,51,49,64,56,46,54,9,9,48,60,59,55,59,9,59,52,56,54,55,56,48,50,58,9,64,57,48,47,50,56,56,61,66,70,71,43,56,55,62,72,69,58,56,46,54,44,48,54,53,49,50,53,58,49,57,55,52,62,64,62,51,59,63,65,57,71,60,69,69,64,64,70,94,104,102,80,82,80,88,92,92,92,84,94,92,76,91,77,79,77,85,81,92,77,90,80,88,102,100,83,95,96,94,92,91,92,92,92,82,86,94,92,80,94,92,76,43,40,44,47,43,42,48,46,43,55,52,41,46,47,59,37,49,43,49,51,37,46,46,40,38,37,43,47,41,37,41,53,46,36,48,41,40,40,41,42,37,35,77,77,12,12,81,81,141,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,87,63,141,99,47,142,62,61,64,70,57,57,65,63,65,51,61,75,75,53,10,10,10,10,10,10,10,10,37,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,22,22,22,22,22", "endOffsets": "375,425,479,533,696,742,791,917,966,1015,1074,1128,1280,1330,1444,1561,1608,1663,1867,2105,2154,2215,2275,2331,2391,2561,2621,2674,2731,2786,2842,2899,2948,2999,3058,3345,3410,3509,3558,3606,3657,6286,6343,6405,6472,6543,6615,6659,6716,15734,15797,15870,15940,15999,16056,16103,16158,16203,16252,16307,16361,16411,16462,16516,16575,16625,16683,16739,16792,16855,16920,16983,17035,17095,17159,17225,17283,17355,17416,17486,17556,17621,17686,17757,20280,20385,20488,20569,20652,20733,20822,20915,21008,21101,21186,21281,21374,21451,21543,21621,21701,21779,21865,21947,22040,22118,22209,22290,22379,22482,22583,22667,22763,22860,22955,23048,23140,23233,23326,23419,23502,23589,23684,23777,23858,23953,24046,24123,26732,26773,26818,26866,26910,26953,27002,27049,27093,27149,27202,27244,27291,27339,27399,27437,27487,27531,27581,27633,27671,27718,27765,27806,27845,27883,27927,27975,28017,28055,28097,28151,28198,28235,28284,28326,28367,28408,28450,28493,28531,28567,30987,31065,31505,31775,35297,35379,35521,35599,35686,35771,35838,35901,35993,36085,36150,36213,36275,36346,36456,36567,36677,36744,36824,36895,36962,37047,37132,37195,37283,37347,38135,38235,38283,38426,38489,38551,38616,38687,38745,38803,38869,38933,38999,39051,39113,39189,39265,39319,118427,118658,118868,119081,119291,119513,119729,119933,119971,120325,121112,121353,121593,121850,122103,122356,122591,122838,123077,123321,123542,123737,124312,124603,124899,125202,125771,126305,126779,126990,127190,127366,182707,186649,191821,201022,202380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28c5dc97a63a31061752728abbdc10f0\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "29,30,33,79,84,85,90,91,92,93,94,95,96,99,100,101,102,104,105,106,107,108,109,110,111,114,115,116,117,118,119,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,147,148,149,150,151,152,153,154,158,159,160,161,162,163,164,165,166,167,170,171,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,261,262,301,302,303,304,305,306,307,326,327,328,329,330,331,332,333,413,414,415,416,466,475,476,479,496,503,504,505,506,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,716,742,743,744,745,746,747,755,756,760,764,768,773,779,786,790,794,799,803,807,811,815,819,823,829,833,839,843,849,853,858,862,865,869,875,879,885,889,895,898,902,906,910,914,918,919,920,921,924,927,930,933,937,938,939,940,941,944,946,948,950,955,956,960,966,970,971,973,985,986,990,996,1000,1001,1002,1006,1033,1037,1038,1042,1070,1242,1268,1439,1465,1496,1504,1510,1526,1548,1553,1558,1568,1577,1586,1590,1597,1616,1623,1624,1633,1636,1639,1643,1647,1651,1654,1655,1660,1665,1675,1680,1687,1693,1694,1697,1701,1706,1708,1710,1713,1716,1718,1722,1725,1732,1735,1738,1742,1744,1748,1750,1752,1754,1758,1766,1774,1786,1792,1801,1804,1815,1818,1819,1824,1825,2015,2084,2154,2155,2165,2174,2175,2177,2181,2184,2187,2190,2193,2196,2199,2202,2206,2209,2212,2215,2219,2222,2226,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2250,2252,2254,2255,2256,2257,2258,2259,2260,2261,2263,2264,2266,2267,2269,2271,2272,2274,2275,2276,2277,2278,2279,2281,2282,2283,2284,2285,2302,2304,2306,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2322,2323,2324,2325,2326,2327,2328,2330,2334,2341,2342,2343,2344,2345,2346,2350,2351,2352,2353,2355,2357,2359,2361,2363,2364,2365,2366,2368,2370,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2386,2387,2388,2389,2391,2393,2394,2396,2397,2399,2401,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2416,2417,2418,2419,2421,2422,2423,2424,2425,2427,2429,2431,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2450,2525,2528,2531,2534,2548,2561,2603,2606,2635,2662,2671,2735,3101,3111,3149,3193,3341,3365,3371,3377,3398,3522,3650,3656,3660,3687,3722,3754,3820,3840,3895,3907,3933", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1133,1188,1335,3415,3662,3717,4039,4103,4173,4234,4309,4385,4462,4700,4785,4867,4943,5061,5138,5216,5322,5428,5507,5587,5644,5833,5907,5982,6047,6113,6173,6721,6793,6866,6933,7001,7060,7119,7178,7237,7296,7350,7404,7457,7511,7565,7619,7894,7968,8047,8120,8194,8265,8337,8409,8623,8680,8738,8811,8885,8959,9034,9106,9179,9249,9410,9470,9573,9642,9711,9781,9855,9931,9995,10072,10148,10225,10290,10359,10436,10511,10580,10648,10725,10791,10852,10949,11014,11083,11182,11253,11312,11370,11427,11486,11550,11621,11693,11765,11837,11909,11976,12044,12112,12171,12234,12298,12388,12479,12539,12605,12672,12738,12808,12872,12925,12992,13053,13120,13233,13291,13354,13419,13484,13559,13632,13704,13748,13795,13841,13890,13951,14012,14073,14135,14199,14263,14327,14392,14455,14515,14576,14642,14701,14761,14823,14894,14954,15510,15596,17925,18015,18102,18190,18272,18355,18445,19737,19789,19847,19892,19958,20022,20079,20136,26251,26308,26356,26405,28746,29207,29254,29410,30315,30671,30735,30797,30857,31780,31854,31924,32002,32056,32126,32211,32259,32305,32366,32429,32495,32559,32630,32693,32758,32822,32883,32944,32996,33069,33143,33212,33287,33361,33435,33576,45824,47733,47811,47901,47989,48085,48175,48757,48846,49093,49374,49626,49911,50304,50781,51003,51225,51501,51728,51958,52188,52418,52648,52875,53294,53520,53945,54175,54603,54822,55105,55313,55444,55671,56097,56322,56749,56970,57395,57515,57791,58092,58416,58707,59021,59158,59289,59394,59636,59803,60007,60215,60486,60598,60710,60815,60932,61146,61292,61432,61518,61866,61954,62200,62618,62867,62949,63047,63704,63804,64056,64480,64735,64829,64918,65155,67179,67421,67523,67776,69932,80613,82129,92824,94352,96109,96735,97155,98416,99681,99937,100173,100720,101214,101819,102017,102597,103965,104340,104458,104996,105153,105349,105622,105878,106048,106189,106253,106618,106985,107661,107925,108263,108616,108710,108896,109202,109464,109589,109716,109955,110166,110285,110478,110655,111110,111291,111413,111672,111785,111972,112074,112181,112310,112585,113093,113589,114466,114760,115330,115479,116211,116383,116467,116803,116895,127677,132908,138279,138341,138919,139503,139594,139707,139936,140096,140248,140419,140585,140754,140921,141084,141327,141497,141670,141841,142115,142314,142519,142849,142933,143029,143125,143223,143323,143425,143527,143629,143731,143833,143933,144029,144141,144270,144393,144524,144655,144753,144867,144961,145101,145235,145331,145443,145543,145659,145755,145867,145967,146107,146243,146407,146537,146695,146845,146986,147130,147265,147377,147527,147655,147783,147919,148051,148181,148311,148423,149703,149849,149993,150131,150197,150287,150363,150467,150557,150659,150767,150875,150975,151055,151147,151245,151355,151407,151485,151591,151683,151787,151897,152019,152182,152508,152588,152688,152778,152888,152978,153219,153313,153419,153511,153611,153723,153837,153953,154069,154163,154277,154389,154491,154611,154733,154815,154919,155039,155165,155263,155357,155445,155557,155673,155795,155907,156082,156198,156284,156376,156488,156612,156679,156805,156873,157001,157145,157273,157342,157437,157552,157665,157764,157873,157984,158095,158196,158301,158401,158531,158622,158745,158839,158951,159037,159141,159237,159325,159443,159547,159651,159777,159865,159973,160073,160163,160273,160357,160459,160543,160597,160661,160767,160853,160963,161047,161306,163922,164040,164155,164235,164596,165133,166537,166615,167959,169320,169708,172551,182712,183050,184721,186654,191826,192577,192839,193039,193418,197696,202385,202614,202765,203820,204903,205753,208779,209523,211654,211994,213305", "endLines": "29,30,33,79,84,85,90,91,92,93,94,95,96,99,100,101,102,104,105,106,107,108,109,110,111,114,115,116,117,118,119,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,147,148,149,150,151,152,153,154,158,159,160,161,162,163,164,165,166,167,170,171,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,261,262,301,302,303,304,305,306,307,326,327,328,329,330,331,332,333,413,414,415,416,466,475,476,479,496,503,504,505,506,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,716,742,743,744,745,746,754,755,759,763,767,772,778,785,789,793,798,802,806,810,814,818,822,828,832,838,842,848,852,857,861,864,868,874,878,884,888,894,897,901,905,909,913,917,918,919,920,923,926,929,932,936,937,938,939,940,943,945,947,949,954,955,959,965,969,970,972,984,985,989,995,999,1000,1001,1005,1032,1036,1037,1041,1069,1241,1267,1438,1464,1495,1503,1509,1525,1547,1552,1557,1567,1576,1585,1589,1596,1615,1622,1623,1632,1635,1638,1642,1646,1650,1653,1654,1659,1664,1674,1679,1686,1692,1693,1696,1700,1705,1707,1709,1712,1715,1717,1721,1724,1731,1734,1737,1741,1743,1747,1749,1751,1753,1757,1765,1773,1785,1791,1800,1803,1814,1817,1818,1823,1824,1829,2083,2153,2154,2164,2173,2174,2176,2180,2183,2186,2189,2192,2195,2198,2201,2205,2208,2211,2214,2218,2221,2225,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2248,2249,2251,2253,2254,2255,2256,2257,2258,2259,2260,2262,2263,2265,2266,2268,2270,2271,2273,2274,2275,2276,2277,2278,2280,2281,2282,2283,2284,2285,2303,2305,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2321,2322,2323,2324,2325,2326,2327,2329,2333,2337,2341,2342,2343,2344,2345,2349,2350,2351,2352,2354,2356,2358,2360,2362,2363,2364,2365,2367,2369,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2385,2386,2387,2388,2390,2392,2393,2395,2396,2398,2400,2402,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2415,2416,2417,2418,2420,2421,2422,2423,2424,2426,2428,2430,2432,2433,2434,2435,2436,2437,2438,2439,2440,2441,2442,2443,2444,2445,2446,2447,2524,2527,2530,2533,2547,2553,2570,2605,2634,2661,2670,2734,3097,3104,3138,3176,3210,3364,3370,3376,3397,3521,3541,3655,3659,3665,3721,3733,3819,3839,3894,3906,3932,3939", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1183,1228,1379,3451,3712,3774,4098,4168,4229,4304,4380,4457,4535,4780,4862,4938,5014,5133,5211,5317,5423,5502,5582,5639,5697,5902,5977,6042,6108,6168,6229,6788,6861,6928,6996,7055,7114,7173,7232,7291,7345,7399,7452,7506,7560,7614,7668,7963,8042,8115,8189,8260,8332,8404,8477,8675,8733,8806,8880,8954,9029,9101,9174,9244,9315,9465,9526,9637,9706,9776,9850,9926,9990,10067,10143,10220,10285,10354,10431,10506,10575,10643,10720,10786,10847,10944,11009,11078,11177,11248,11307,11365,11422,11481,11545,11616,11688,11760,11832,11904,11971,12039,12107,12166,12229,12293,12383,12474,12534,12600,12667,12733,12803,12867,12920,12987,13048,13115,13228,13286,13349,13414,13479,13554,13627,13699,13743,13790,13836,13885,13946,14007,14068,14130,14194,14258,14322,14387,14450,14510,14571,14637,14696,14756,14818,14889,14949,15017,15591,15678,18010,18097,18185,18267,18350,18440,18531,19784,19842,19887,19953,20017,20074,20131,20185,26303,26351,26400,26451,28775,29249,29298,29451,30342,30730,30792,30852,30909,31849,31919,31997,32051,32121,32206,32254,32300,32361,32424,32490,32554,32625,32688,32753,32817,32878,32939,32991,33064,33138,33207,33282,33356,33430,33571,33641,45872,47806,47896,47984,48080,48170,48752,48841,49088,49369,49621,49906,50299,50776,50998,51220,51496,51723,51953,52183,52413,52643,52870,53289,53515,53940,54170,54598,54817,55100,55308,55439,55666,56092,56317,56744,56965,57390,57510,57786,58087,58411,58702,59016,59153,59284,59389,59631,59798,60002,60210,60481,60593,60705,60810,60927,61141,61287,61427,61513,61861,61949,62195,62613,62862,62944,63042,63699,63799,64051,64475,64730,64824,64913,65150,67174,67416,67518,67771,69927,80608,82124,92819,94347,96104,96730,97150,98411,99676,99932,100168,100715,101209,101814,102012,102592,103960,104335,104453,104991,105148,105344,105617,105873,106043,106184,106248,106613,106980,107656,107920,108258,108611,108705,108891,109197,109459,109584,109711,109950,110161,110280,110473,110650,111105,111286,111408,111667,111780,111967,112069,112176,112305,112580,113088,113584,114461,114755,115325,115474,116206,116378,116462,116798,116890,117168,132903,138274,138336,138914,139498,139589,139702,139931,140091,140243,140414,140580,140749,140916,141079,141322,141492,141665,141836,142110,142309,142514,142844,142928,143024,143120,143218,143318,143420,143522,143624,143726,143828,143928,144024,144136,144265,144388,144519,144650,144748,144862,144956,145096,145230,145326,145438,145538,145654,145750,145862,145962,146102,146238,146402,146532,146690,146840,146981,147125,147260,147372,147522,147650,147778,147914,148046,148176,148306,148418,148558,149844,149988,150126,150192,150282,150358,150462,150552,150654,150762,150870,150970,151050,151142,151240,151350,151402,151480,151586,151678,151782,151892,152014,152177,152334,152583,152683,152773,152883,152973,153214,153308,153414,153506,153606,153718,153832,153948,154064,154158,154272,154384,154486,154606,154728,154810,154914,155034,155160,155258,155352,155440,155552,155668,155790,155902,156077,156193,156279,156371,156483,156607,156674,156800,156868,156996,157140,157268,157337,157432,157547,157660,157759,157868,157979,158090,158191,158296,158396,158526,158617,158740,158834,158946,159032,159136,159232,159320,159438,159542,159646,159772,159860,159968,160068,160158,160268,160352,160454,160538,160592,160656,160762,160848,160958,161042,161162,163917,164035,164150,164230,164591,164824,165645,166610,167954,169315,169703,172546,182599,182842,184415,186073,187221,192572,192834,193034,193413,197691,198297,202609,202760,202975,204898,205210,208774,209518,211649,211989,213300,213503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a088b58985e0669f5f433ca0cae0ef30\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "546", "startColumns": "4", "startOffsets": "33646", "endColumns": "82", "endOffsets": "33724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74d3d7c2d24a7100d6b0d87b145b1bf3\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "35,97,98,112,113,144,145,254,255,256,257,258,259,260,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,378,379,380,470,471,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,510,548,549,550,551,552,553,554,722,2286,2287,2292,2295,2300,2448,2449,3105,3139,3211,3246,3276,3309", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1449,4540,4612,5702,5767,7673,7742,15022,15092,15160,15232,15302,15363,15437,18759,18820,18881,18943,19007,19069,19130,19198,19298,19358,19424,19497,19566,19623,19675,24128,24200,24276,28955,28990,29456,29511,29574,29629,29687,29745,29806,29869,29926,29977,30027,30088,30145,30211,30245,30280,31143,33771,33838,33910,33979,34048,34122,34194,46101,148563,148680,148947,149240,149507,161167,161239,182847,184420,187226,189032,190032,190714", "endLines": "35,97,98,112,113,144,145,254,255,256,257,258,259,260,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,378,379,380,470,471,480,481,482,483,484,485,486,487,488,489,490,491,492,493,494,495,510,548,549,550,551,552,553,554,722,2286,2290,2292,2298,2300,2448,2449,3110,3148,3245,3266,3308,3314", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1504,4607,4695,5762,5828,7737,7800,15087,15155,15227,15297,15358,15432,15505,18815,18876,18938,19002,19064,19125,19193,19293,19353,19419,19492,19561,19618,19670,19732,24195,24271,24336,28985,29020,29506,29569,29624,29682,29740,29801,29864,29921,29972,30022,30083,30140,30206,30240,30275,30310,31208,33833,33905,33974,34043,34117,34189,34277,46167,148675,148876,149052,149436,149631,161234,161301,183045,184716,189027,189708,190709,190876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5896d35c6783ae896c0749c992e3f857\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "464", "startColumns": "4", "startOffsets": "28629", "endColumns": "65", "endOffsets": "28690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\de37a3e5059d5af25fd93109397e5087\\transformed\\navigation-runtime-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "473,2554,3570,3573", "startColumns": "4,4,4,4", "startOffsets": "29087,164829,199565,199680", "endLines": "473,2560,3572,3575", "endColumns": "52,24,24,24", "endOffsets": "29135,165128,199675,199790"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eb7ca73e61962373954ac7aef0f2b6dc\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "463,478,501,3267,3272", "startColumns": "4,4,4,4,4", "startOffsets": "28572,29345,30554,189713,189883", "endLines": "463,478,501,3271,3275", "endColumns": "56,64,63,24,24", "endOffsets": "28624,29405,30613,189878,190027"}}, {"source": "C:\\Users\\<USER>\\Desktop\\rv\\Downloader\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,20,17,25,8,9,11,12,16,23,10,7,22,13,14,5,3,18,15,4,19,21,6,24,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "57,1081,940,1332,375,454,557,622,877,1214,501,316,1165,683,736,216,100,997,800,171,1038,1124,265,1273,1406", "endColumns": "41,41,55,72,77,45,63,59,61,57,54,57,47,51,62,47,69,39,75,43,41,39,49,57,59", "endOffsets": "94,1118,991,1400,448,495,616,677,934,1267,551,369,1208,730,794,259,165,1032,871,210,1075,1159,310,1326,1461"}, "to": {"startLines": "547,555,556,557,560,563,564,565,566,567,568,623,624,642,705,707,708,709,710,711,714,715,718,738,741", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "33729,34282,34324,34380,34572,34817,34863,34927,34987,35049,35107,39324,39382,40688,45243,45360,45408,45478,45518,45594,45742,45784,45923,47504,47673", "endColumns": "41,41,55,72,77,45,63,59,61,57,54,57,47,51,62,47,69,39,75,43,41,39,49,57,59", "endOffsets": "33766,34319,34375,34448,34645,34858,34922,34982,35044,35102,35157,39377,39425,40735,45301,45403,45473,45513,45589,45633,45779,45819,45968,47557,47728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b663345c01d46f97ade444988d759139\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "499", "startColumns": "4", "startOffsets": "30450", "endColumns": "53", "endOffsets": "30499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\eaf06e46581841a7a8a85fdefd8b654d\\transformed\\navigation-common-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3542,3555,3561,3567,3576", "startColumns": "4,4,4,4,4", "startOffsets": "198302,198941,199185,199432,199795", "endLines": "3554,3560,3566,3569,3580", "endColumns": "24,24,24,24,24", "endOffsets": "198936,199180,199427,199560,199972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d62d9a540e552a1187e018192472b047\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "509,643,644,645,646,647,648,649,650,651,652,655,656,657,658,659,660,661,662,663,664,665,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,1833,1843", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "31070,40740,40828,40914,40995,41079,41148,41213,41296,41402,41488,41608,41662,41731,41792,41861,41950,42045,42119,42216,42309,42407,42556,42647,42735,42831,42929,42993,43061,43148,43242,43309,43381,43453,43554,43663,43739,43808,43856,43922,43986,44060,44117,44174,44246,44296,44350,44421,44492,44562,44631,44689,44765,44836,44910,44996,45046,45116,117285,118000", "endLines": "509,643,644,645,646,647,648,649,650,651,654,655,656,657,658,659,660,661,662,663,664,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,701,702,703,1842,1845", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "31138,40823,40909,40990,41074,41143,41208,41291,41397,41483,41603,41657,41726,41787,41856,41945,42040,42114,42211,42304,42402,42551,42642,42730,42826,42924,42988,43056,43143,43237,43304,43376,43448,43549,43658,43734,43803,43851,43917,43981,44055,44112,44169,44241,44291,44345,44416,44487,44557,44626,44684,44760,44831,44905,44991,45041,45111,45176,117995,118148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ffbb8e229a655f09c66ebc47ebb2be16\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "468,474", "startColumns": "4,4", "startOffsets": "28835,29140", "endColumns": "53,66", "endOffsets": "28884,29202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1cebe35438d2be6bd111f5c50709e369\\transformed\\recyclerview-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,30", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,1398"}, "to": {"startLines": "39,298,299,300,308,309,310,469,3666", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1668,17762,17821,17869,18536,18611,18687,28889,202980", "endLines": "39,298,299,300,308,309,310,469,3686", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "1719,17816,17864,17920,18606,18682,18754,28950,203815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6473ba241fc8d06e2c8f9454c5d48c0a\\transformed\\coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "418", "startColumns": "4", "startOffsets": "26530", "endColumns": "49", "endOffsets": "26575"}}, {"source": "C:\\Users\\<USER>\\Desktop\\rv\\Downloader\\app\\src\\main\\res\\values\\file_types.xml", "from": {"startLines": "17,9,4,14,11,13,6,10,16,12,5,7,8,15,3,26,21,31,28,30,23,27,29,22,24,25,20", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1183,571,179,948,730,868,337,651,1098,798,256,409,491,1018,94,1901,1396,2373,2087,2279,1605,1998,2190,1500,1708,1803,1293", "endColumns": "70,78,75,68,66,78,70,77,83,68,79,80,78,78,83,95,102,89,101,92,101,87,87,103,93,96,101", "endOffsets": "1249,645,250,1012,792,942,403,724,1177,862,331,485,565,1092,173,1992,1494,2458,2184,2367,1702,2081,2273,1599,1797,1895,1390"}, "to": {"startLines": "625,626,627,628,629,630,631,632,633,634,635,636,637,638,639,723,724,725,726,727,728,729,730,731,732,733,734", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39430,39501,39580,39656,39725,39792,39871,39942,40020,40104,40173,40253,40334,40413,40492,46172,46268,46371,46461,46563,46656,46758,46846,46934,47038,47132,47229", "endColumns": "70,78,75,68,66,78,70,77,83,68,79,80,78,78,83,95,102,89,101,92,101,87,87,103,93,96,101", "endOffsets": "39496,39575,39651,39720,39787,39866,39937,40015,40099,40168,40248,40329,40408,40487,40571,46263,46366,46456,46558,46651,46753,46841,46929,47033,47127,47224,47326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\735e093a8b8e369710032a5f563fa25e\\transformed\\work-runtime-2.10.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "86,87,88,89", "startColumns": "4,4,4,4", "startOffsets": "3779,3844,3914,3978", "endColumns": "64,69,63,60", "endOffsets": "3839,3909,3973,4034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\25e66c178efea8acfd295bf88ce9f274\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "477,498", "startColumns": "4,4", "startOffsets": "29303,30390", "endColumns": "41,59", "endOffsets": "29340,30445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2e2286c2e0090f544c5c68ec9b9000d0\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2571,2587,2593,3734,3750", "startColumns": "4,4,4,4,4", "startOffsets": "165650,166075,166253,205215,205626", "endLines": "2586,2592,2602,3749,3753", "endColumns": "24,24,24,24,24", "endOffsets": "166070,166248,166532,205621,205748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28aefb379186de514fb03bc514340fbf\\transformed\\media-1.6.0\\res\\values\\values.xml", "from": {"startLines": "2,5,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,288,410,476,598,659,725", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "139,345,471,593,654,720,787"}, "to": {"startLines": "146,472,2291,2293,2294,2299,2301", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7805,29025,148881,149057,149179,149441,149636", "endColumns": "88,61,65,121,60,65,66", "endOffsets": "7889,29082,148942,149174,149235,149502,149698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7aa4dd35acc84f087f7df6becf4b1038\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "739,740", "startColumns": "4,4", "startOffsets": "47562,47618", "endColumns": "55,54", "endOffsets": "47613,47668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1540d281ce33e276dcf8397133c79052\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "500", "startColumns": "4", "startOffsets": "30504", "endColumns": "49", "endOffsets": "30549"}}, {"source": "C:\\Users\\<USER>\\Desktop\\rv\\Downloader\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "57", "endLines": "4", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "2338", "startColumns": "4", "startOffsets": "152339", "endLines": "2340", "endColumns": "12", "endOffsets": "152503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\478b3be060432db7073f96b7c2278ef6\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,417,419,420,465,467,502,558,559,561,562,569,640,641,704,706,712,713,717,719,720,721,735,736,737,1830,2008,2011", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24341,24400,24459,24519,24579,24639,24699,24759,24819,24879,24939,24999,25059,25118,25178,25238,25298,25358,25418,25478,25538,25598,25658,25718,25777,25837,25897,25956,26015,26074,26133,26192,26456,26580,26638,28695,28780,30618,34453,34518,34650,34716,35162,40576,40628,45181,45306,45638,45688,45877,45973,46019,46061,47331,47378,47414,117173,127371,127482", "endLines": "381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,417,419,420,465,467,502,558,559,561,562,569,640,641,704,706,712,713,717,719,720,721,735,736,737,1832,2010,2014", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "24395,24454,24514,24574,24634,24694,24754,24814,24874,24934,24994,25054,25113,25173,25233,25293,25353,25413,25473,25533,25593,25653,25713,25772,25832,25892,25951,26010,26069,26128,26187,26246,26525,26633,26688,28741,28830,30666,34513,34567,34711,34812,35215,40623,40683,45238,45355,45683,45737,45918,46014,46056,46096,47373,47409,47499,117280,127477,127672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5520295d841dd9ac5479b8fdfb6f2e95\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "497", "startColumns": "4", "startOffsets": "30347", "endColumns": "42", "endOffsets": "30385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f05148d81958cc6d8e30b6d34a1ab13\\transformed\\media3-exoplayer-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "596,597,598,599,600,601,602,603,604", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "37352,37422,37484,37549,37613,37690,37755,37845,37929", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "37417,37479,37544,37608,37685,37750,37840,37924,37993"}}]}]}