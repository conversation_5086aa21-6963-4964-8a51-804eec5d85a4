# Download App Testing Guide

This guide provides comprehensive testing instructions to verify that the Universal Downloader app can successfully download YouTube videos, handle Instagram URLs, and download direct files.

## Prerequisites

1. **Android Device/Emulator**: Android 7.0 (API 24) or higher
2. **Internet Connection**: Required for downloading content
3. **Storage Permissions**: Grant storage permissions when prompted
4. **APK Installation**: Install the debug APK from `app/build/outputs/apk/debug/app-debug.apk`

## Test Categories

### 1. URL Validation Tests

Test the app's ability to validate different URL formats:

#### Valid URLs (Should be accepted):
- `https://example.com/video.mp4`
- `http://example.com/image.jpg`
- `example.com/document.pdf`
- `https://www.youtube.com/watch?v=dQw4w9WgXcQ`
- `https://instagram.com/p/ABC123/`

#### Invalid URLs (Should show "Invalid URL" error):
- `not-a-url`
- `ftp://example.com/file.txt`
- `javascript:alert('test')`
- `` (empty string)
- `   ` (spaces only)

**Expected Behavior**: Valid URLs should be accepted, invalid URLs should show an error message.

### 2. YouTube Download Tests

Test YouTube URL detection and download functionality:

#### YouTube URL Formats to Test:
- Standard: `https://www.youtube.com/watch?v=dQw4w9WgXcQ`
- Short: `https://youtu.be/dQw4w9WgXcQ`
- Mobile: `https://m.youtube.com/watch?v=dQw4w9WgXcQ`
- Music: `https://music.youtube.com/watch?v=dQw4w9WgXcQ`
- Shorts: `https://www.youtube.com/shorts/dQw4w9WgXcQ`

**Expected Behavior**: 
- App should detect these as YouTube URLs
- Show "Extracting YouTube video...please wait" message
- Either successfully extract and start download, or show appropriate error message
- Download should appear in the downloads list

### 3. Instagram URL Tests

Test Instagram URL detection:

#### Instagram URL Formats to Test:
- Post: `https://www.instagram.com/p/ABC123/`
- Reel: `https://www.instagram.com/reel/XYZ789/`
- TV: `https://www.instagram.com/tv/DEF456/`
- Short URL: `https://instagr.am/p/ABC123/`

**Expected Behavior**:
- App should detect these as Instagram URLs
- Show message: "Instagram downloads are not yet fully supported. Please use a direct video/image URL instead."

### 4. Direct File Download Tests

Test direct file downloads:

#### Test URLs for Different File Types:

**Video Files:**
- MP4: `https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4`
- WebM: `https://file-examples.com/storage/fe68c1e7b5d1d82b9b8c1a8/2017/10/file_example_WEBM_1280_3_6MB.webm`

**Image Files:**
- JPG: `https://picsum.photos/800/600.jpg`
- PNG: `https://picsum.photos/800/600.png`

**Audio Files:**
- MP3: `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`

**Documents:**
- PDF: `https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf`

**Expected Behavior**:
- Downloads should start immediately
- Progress notification should appear
- Files should be saved to appropriate directories
- Downloads should appear in the downloads list

### 5. Clipboard Monitoring Tests

Test automatic URL detection from clipboard:

#### Steps:
1. Copy a valid URL to clipboard (outside the app)
2. Open the app
3. Check if clipboard dialog appears

**Expected Behavior**:
- App should detect valid URLs from clipboard
- Show dialog asking if you want to use the clipboard URL
- Tapping "Use" should populate the URL field

### 6. Error Handling Tests

Test app behavior with problematic URLs:

#### Test Cases:
- Non-existent domain: `https://this-domain-does-not-exist-12345.com/video.mp4`
- 404 URL: `https://httpstat.us/404`
- Timeout URL: `https://httpstat.us/200?sleep=30000`
- Large file: Test with a very large file URL

**Expected Behavior**:
- App should handle errors gracefully
- Show appropriate error messages
- App should not crash

### 7. UI and State Management Tests

Test user interface and state management:

#### Test Cases:
1. **URL Input**: Type and edit URLs in the input field
2. **Clear Messages**: Error/success messages should be clearable
3. **Download History**: Downloads should appear in history
4. **Clear History**: History should be clearable
5. **App Rotation**: State should persist during device rotation
6. **Background/Foreground**: Downloads should continue when app is backgrounded

## Manual Testing Checklist

Use this checklist to systematically test the app:

### Basic Functionality
- [ ] App launches without crashing
- [ ] URL input field accepts text
- [ ] Download button is clickable
- [ ] Storage permissions are requested and granted

### URL Validation
- [ ] Valid URLs are accepted
- [ ] Invalid URLs show error message
- [ ] Error messages are clear and helpful

### YouTube Downloads
- [ ] YouTube URLs are detected correctly
- [ ] Extraction process starts with appropriate message
- [ ] Downloads appear in history (even if extraction fails)
- [ ] Error handling works for invalid YouTube URLs

### Instagram URLs
- [ ] Instagram URLs are detected correctly
- [ ] "Not yet supported" message is shown
- [ ] App doesn't crash with Instagram URLs

### Direct Downloads
- [ ] Direct file URLs start downloads immediately
- [ ] Progress notifications appear
- [ ] Files are saved to device storage
- [ ] Different file types are handled correctly

### Clipboard Integration
- [ ] Clipboard monitoring works
- [ ] Dialog appears for valid URLs
- [ ] "Use" button populates URL field correctly
- [ ] "Dismiss" button closes dialog

### Error Handling
- [ ] Network errors are handled gracefully
- [ ] Invalid URLs show appropriate errors
- [ ] App doesn't crash with malformed input
- [ ] Timeout scenarios are handled

### UI/UX
- [ ] Interface is responsive
- [ ] Messages are clear and helpful
- [ ] Downloads list updates correctly
- [ ] State persists during app lifecycle changes

## Automated Test Execution

To run the automated test suite:

### Windows:
```bash
run_tests.bat
```

### Linux/Mac:
```bash
chmod +x run_tests.sh
./run_tests.sh
```

### Individual Test Commands:
```bash
# Unit tests only
./gradlew test

# Instrumented tests (requires device)
./gradlew connectedAndroidTest

# Lint checks
./gradlew lint

# Build APK
./gradlew assembleDebug
```

## Test Reports

After running tests, check these locations for reports:

- **Unit Test Report**: `app/build/reports/tests/testDebugUnitTest/index.html`
- **Instrumented Test Report**: `app/build/reports/androidTests/connected/index.html`
- **Lint Report**: `app/build/reports/lint-results.html`
- **APK Location**: `app/build/outputs/apk/debug/app-debug.apk`

## Expected Test Results

### Unit Tests
- All URL validation tests should pass
- YouTube URL detection tests should pass
- Instagram URL detection tests should pass
- File utility tests should pass

### Integration Tests
- Basic download flow tests should pass
- Error handling tests should pass
- State management tests should pass

### Manual Testing
- YouTube URL detection should work
- Instagram URLs should show "not supported" message
- Direct file downloads should work for small test files
- App should handle errors gracefully without crashing

## Troubleshooting

### Common Issues:
1. **Tests fail**: Check internet connection and device setup
2. **APK won't install**: Enable "Install from unknown sources"
3. **Downloads fail**: Check storage permissions
4. **YouTube extraction fails**: This is expected for some URLs due to YouTube's restrictions

### Success Criteria:
- App launches and runs without crashing
- URL validation works correctly
- YouTube URLs are detected (extraction may fail, but detection should work)
- Instagram URLs show appropriate "not supported" message
- Direct file downloads work for test URLs
- Error handling prevents crashes
