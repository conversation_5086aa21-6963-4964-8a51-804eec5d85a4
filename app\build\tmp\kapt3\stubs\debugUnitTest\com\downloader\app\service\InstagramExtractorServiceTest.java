package com.downloader.app.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\r\u001a\u00020\u000eH\u0007J\b\u0010\u000f\u001a\u00020\u000eH\u0007J\b\u0010\u0010\u001a\u00020\u000eH\u0007J\b\u0010\u0011\u001a\u00020\u000eH\u0007J\b\u0010\u0012\u001a\u00020\u000eH\u0007J\f\u0010\u0013\u001a\u00060\u000ej\u0002`\u0014H\u0007J\b\u0010\u0015\u001a\u00020\u000eH\u0007J\b\u0010\u0016\u001a\u00020\u000eH\u0007J\b\u0010\u0017\u001a\u00020\u000eH\u0007J\b\u0010\u0018\u001a\u00020\u000eH\u0007J\b\u0010\u0019\u001a\u00020\u000eH\u0007J\b\u0010\u001a\u001a\u00020\u000eH\u0007J\b\u0010\u001b\u001a\u00020\u000eH\u0007J\b\u0010\u001c\u001a\u00020\u000eH\u0007J\b\u0010\u001d\u001a\u00020\u000eH\u0007J\b\u0010\u001e\u001a\u00020\u000eH\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u0013\u0010\u0007\u001a\u00020\b8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/downloader/app/service/InstagramExtractorServiceTest;", "", "()V", "context", "Landroid/content/Context;", "instagramExtractorService", "Lcom/downloader/app/service/InstagramExtractorService;", "instantTaskExecutorRule", "Landroidx/arch/core/executor/testing/InstantTaskExecutorRule;", "getInstantTaskExecutorRule", "()Landroidx/arch/core/executor/testing/InstantTaskExecutorRule;", "testDispatcher", "Lkotlinx/coroutines/test/TestDispatcher;", "extractContentId should extract ID from TV URLs", "", "extractContentId should extract ID from post URLs", "extractContentId should extract ID from reel URLs", "extractContentId should extract ID from story URLs", "extractContentId should return empty string for invalid URLs", "extractInstagramUrl should return failure for current implementation", "Lkotlinx/coroutines/test/TestResult;", "getInstagramContentType should identify TV URLs", "getInstagramContentType should identify post URLs", "getInstagramContentType should identify reel URLs", "getInstagramContentType should identify story URLs", "getInstagramContentType should return UNKNOWN for invalid URLs", "normalizeInstagramUrl should add HTTPS protocol", "normalizeInstagramUrl should handle instagr_am short URLs", "normalizeInstagramUrl should remove trailing slash", "setup", "tearDown", "app_debugUnitTest"})
@kotlin.OptIn(markerClass = {kotlinx.coroutines.ExperimentalCoroutinesApi.class})
public final class InstagramExtractorServiceTest {
    @org.jetbrains.annotations.NotNull()
    private final androidx.arch.core.executor.testing.InstantTaskExecutorRule instantTaskExecutorRule = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.test.TestDispatcher testDispatcher = null;
    private android.content.Context context;
    private com.downloader.app.service.InstagramExtractorService instagramExtractorService;
    
    public InstagramExtractorServiceTest() {
        super();
    }
    
    @org.junit.Rule()
    @org.jetbrains.annotations.NotNull()
    public final androidx.arch.core.executor.testing.InstantTaskExecutorRule getInstantTaskExecutorRule() {
        return null;
    }
    
    @org.junit.Before()
    public final void setup() {
    }
    
    @org.junit.After()
    public final void tearDown() {
    }
}