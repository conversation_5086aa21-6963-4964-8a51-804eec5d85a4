package com.downloader.app.service

import android.content.Context
import android.util.Log
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.data.model.DownloadStatus
import com.downloader.app.data.model.FileType
import com.downloader.app.utils.FileUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.File
import java.util.Date
import java.util.regex.Pattern
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class InstagramExtractorService @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "InstagramExtractor"
        
        // Instagram URL patterns for different content types
        private val POST_PATTERN = Pattern.compile("instagram\\.com/p/([A-Za-z0-9_-]+)")
        private val REEL_PATTERN = Pattern.compile("instagram\\.com/reel/([A-Za-z0-9_-]+)")
        private val TV_PATTERN = Pattern.compile("instagram\\.com/tv/([A-Za-z0-9_-]+)")
        private val STORY_PATTERN = Pattern.compile("instagram\\.com/stories/([A-Za-z0-9_.]+)/([0-9]+)")
    }

    private val okHttpClient = OkHttpClient.Builder()
        .followRedirects(true)
        .followSslRedirects(true)
        .build()

    suspend fun extractInstagramUrl(instagramUrl: String): Result<DownloadItem> = withContext(Dispatchers.IO) {
        try {
            val normalizedUrl = normalizeInstagramUrl(instagramUrl)
            Log.d(TAG, "Extracting from normalized URL: $normalizedUrl")
            
            // For now, return a placeholder indicating Instagram support is limited
            // In a real implementation, you would:
            // 1. Parse the Instagram page HTML
            // 2. Extract video/image URLs from the page source
            // 3. Handle Instagram's anti-scraping measures
            // 4. Deal with authentication requirements
            
            val contentId = extractContentId(normalizedUrl)
            if (contentId.isEmpty()) {
                return@withContext Result.failure(Exception("Could not extract content ID from Instagram URL"))
            }
            
            // For now, return an error indicating limited support
            // In a real implementation, you would create a DownloadItem here
            return@withContext Result.failure(Exception("Instagram downloads require additional implementation. Please use a direct media URL instead."))
            
        } catch (e: Exception) {
            Log.e(TAG, "Instagram extraction error", e)
            return@withContext Result.failure(e)
        }
    }

    private fun normalizeInstagramUrl(url: String): String {
        var normalizedUrl = url.trim()
        
        // Handle instagr.am short URLs
        if (normalizedUrl.contains("instagr.am")) {
            normalizedUrl = normalizedUrl.replace("instagr.am", "instagram.com")
        }
        
        // Ensure HTTPS
        if (!normalizedUrl.startsWith("http")) {
            normalizedUrl = "https://$normalizedUrl"
        }
        
        // Remove trailing slash
        if (normalizedUrl.endsWith("/")) {
            normalizedUrl = normalizedUrl.dropLast(1)
        }
        
        return normalizedUrl
    }

    private fun extractContentId(url: String): String {
        // Try to extract content ID from different Instagram URL patterns
        POST_PATTERN.matcher(url).let { matcher ->
            if (matcher.find()) {
                return matcher.group(1) ?: ""
            }
        }
        
        REEL_PATTERN.matcher(url).let { matcher ->
            if (matcher.find()) {
                return matcher.group(1) ?: ""
            }
        }
        
        TV_PATTERN.matcher(url).let { matcher ->
            if (matcher.find()) {
                return matcher.group(1) ?: ""
            }
        }
        
        STORY_PATTERN.matcher(url).let { matcher ->
            if (matcher.find()) {
                val username = matcher.group(1) ?: ""
                val storyId = matcher.group(2) ?: ""
                return "${username}_${storyId}"
            }
        }
        
        return ""
    }

    fun getInstagramContentType(url: String): InstagramContentType {
        return when {
            POST_PATTERN.matcher(url).find() -> InstagramContentType.POST
            REEL_PATTERN.matcher(url).find() -> InstagramContentType.REEL
            TV_PATTERN.matcher(url).find() -> InstagramContentType.TV
            STORY_PATTERN.matcher(url).find() -> InstagramContentType.STORY
            else -> InstagramContentType.UNKNOWN
        }
    }

    enum class InstagramContentType {
        POST, REEL, TV, STORY, UNKNOWN
    }
}
