package com.downloader.app.ui.screens

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("About Dawn") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            AppHeader()
            ExpandableCard(title = "App Information", icon = Icons.Default.Info) {
                InfoSectionContent(
                    items = listOf(
                        InfoItem("Version", "1.0.0"),
                        InfoItem("Build", "Release"),
                        InfoItem("Target SDK", "Android 14 (API 34)"),
                        InfoItem("Min SDK", "Android 7.0 (API 24)")
                    )
                )
            }
            ExpandableCard(title = "Key Features", icon = Icons.Default.Star) {
                FeaturesSectionContent()
            }
            ExpandableCard(title = "Supported File Types", icon = Icons.Default.FileCopy) {
                FileSupportSectionContent()
            }
            ExpandableCard(title = "Technical Details", icon = Icons.Default.Build) {
                TechnicalSectionContent()
            }
            ExpandableCard(title = "Privacy & Security", icon = Icons.Default.Security) {
                PrivacySectionContent()
            }
            ExpandableCard(title = "Development", icon = Icons.Default.Code) {
                DeveloperSectionContent()
            }
            Footer()
        }
    }
}

@Composable
private fun AppHeader() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(vertical = 16.dp)
    ) {
        Icon(
            imageVector = Icons.Default.WbSunny,
            contentDescription = "Dawn Logo",
            modifier = Modifier.size(80.dp),
            tint = MaterialTheme.colorScheme.primary
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(
            text = "Dawn",
            style = MaterialTheme.typography.headlineLarge,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = "Universal Download Manager",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun Footer() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(top = 16.dp)
    ) {
        Text(
            text = "Made with ❤️ for Android",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Spacer(modifier = Modifier.height(8.dp))
        val context = LocalContext.current
        Text(
            text = "Developed by Xenonesis",
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.primary,
                textDecoration = TextDecoration.Underline
            ),
            modifier = Modifier.clickable {
                val intent = android.content.Intent(android.content.Intent.ACTION_VIEW)
                intent.data = android.net.Uri.parse("https://iaddy.netlify.app/")
                context.startActivity(intent)
            }
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = "© 2025 Dawn. All rights reserved.",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
private fun ExpandableCard(
    title: String,
    icon: ImageVector,
    content: @Composable () -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    val rotationState by animateFloatAsState(targetValue = if (expanded) 180f else 0f, label = "rotation")

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        onClick = { expanded = !expanded }
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(icon, contentDescription = null, modifier = Modifier.size(24.dp))
                Spacer(modifier = Modifier.width(16.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
                Icon(
                    imageVector = Icons.Default.ArrowDropDown,
                    contentDescription = "Expand",
                    modifier = Modifier.graphicsLayer(rotationZ = rotationState)
                )
            }

            if (expanded) {
                Spacer(modifier = Modifier.height(16.dp))
                content()
            }
        }
    }
}

@Composable
private fun InfoSectionContent(
    items: List<InfoItem>
) {
    Column {
        items.forEach { item ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = item.label,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = item.value,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun FeaturesSectionContent() {
    val features = listOf(
        FeatureItem(Icons.Default.Download, "Universal Downloads", "Download any file type from any URL"),
        FeatureItem(Icons.Default.Visibility, "Instant Preview", "Preview files directly in the app"),
        FeatureItem(Icons.Default.Folder, "Smart Organization", "Automatic file categorization and storage"),
        FeatureItem(Icons.Default.Pause, "Download Control", "Pause, resume, cancel, and retry downloads"),
        FeatureItem(Icons.Default.Notifications, "Background Downloads", "Continue downloading when app is closed"),
        FeatureItem(Icons.Default.ContentPaste, "Clipboard Detection", "Auto-detect URLs from clipboard"),
        FeatureItem(Icons.Default.History, "Download History", "Complete history with search and filtering"),
        FeatureItem(Icons.Default.Share, "File Sharing", "Share downloaded files with other apps")
    )

    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
        features.forEach { feature ->
            FeatureRow(feature)
        }
    }
}

@Composable
private fun FileSupportSectionContent() {
    val fileTypes = listOf(
        "🎬 Videos (20+ formats): MP4, AVI, MKV, MOV, WebM, FLV, 3GP...",
        "🎵 Audio (20+ formats): MP3, WAV, FLAC, AAC, OGG, OPUS...",
        "🖼️ Images (25+ formats): JPG, PNG, GIF, WebP, SVG, HEIC...",
        "📄 Documents (15+ formats): PDF, DOC, TXT, RTF, MD...",
        "📊 Presentations: PPT, PPTX, ODP, KEY, PPS...",
        "📈 Spreadsheets: XLS, XLSX, CSV, ODS, Numbers...",
        "📚 eBooks: EPUB, MOBI, AZW, FB2, LIT...",
        "💻 Source Code (30+ languages): Python, Java, Kotlin, C++...",
        "🗜️ Archives: ZIP, RAR, 7Z, TAR, ISO, DMG...",
        "🔤 Fonts: TTF, OTF, WOFF, WOFF2...",
        "🗄️ Databases: SQLite, MDB, DBF...",
        "🏗️ CAD Files: DWG, DXF, STEP, STL...",
        "🎭 3D Models: 3DS, Blend, FBX, OBJ...",
        "⚙️ Executables: EXE, MSI, DEB, RPM...",
        "🎨 Vector Graphics: SVG, AI, EPS...",
        "📱 Android Apps: APK, AAB..."
    )

    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
        fileTypes.forEach { type ->
            Text(
                text = "• $type",
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Composable
private fun TechnicalSectionContent() {
    val techDetails = listOf(
        "🏗️ Architecture: MVVM with Clean Architecture",
        "🎨 UI Framework: Jetpack Compose with Material 3",
        "💉 Dependency Injection: Hilt (Dagger)",
        "💾 Download history stored locally (no database)",
        "🌐 Networking: OkHttp for robust file downloads",
        "🔄 Async Operations: Kotlin Coroutines",
        "📱 Background Processing: WorkManager & Foreground Service",
        "🖼️ Image Loading: Coil for efficient image handling",
        "📁 File Sharing: FileProvider for secure file access",
        "🔔 Notifications: Rich download progress notifications",
        "🎯 State Management: StateFlow for reactive UI",
        "📊 Performance: Memory-efficient with smart caching"
    )

    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
        techDetails.forEach { detail ->
            Text(
                text = detail,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Composable
private fun PrivacySectionContent() {
    val privacyFeatures = listOf(
        "🔒 Complete Privacy: No data collection or tracking",
        "📱 Local Storage: All files stored locally on your device",
        "🛡️ Secure Operations: Proper Android permission handling",
        "🔐 Safe File Sharing: FileProvider for secure file access",
        "🚫 No Cloud Dependencies: Works entirely offline",
        "🔍 No Analytics: No usage tracking or data mining",
        "💾 Local Database: Download history stored locally",
        "🛠️ Open Source Ready: Transparent and auditable code"
    )

    Column(verticalArrangement = Arrangement.spacedBy(4.dp)) {
        privacyFeatures.forEach { feature ->
            Text(
                text = feature,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

@Composable
private fun DeveloperSectionContent() {
    Column {
        Text(
            text = "Dawn is built with modern Android development practices and the latest technologies. The app follows Material Design guidelines and implements clean architecture patterns for maintainability and scalability.",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Text(
            text = "Built with:",
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 4.dp)
        )

        Text(
            text = "• Kotlin programming language\n• Android Jetpack libraries\n• Material 3 design system\n• Modern Android architecture components",
            style = MaterialTheme.typography.bodySmall
        )
    }
}

@Composable
private fun FeatureRow(feature: FeatureItem) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = feature.icon,
            contentDescription = null,
            modifier = Modifier.size(20.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        Spacer(modifier = Modifier.width(12.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = feature.title,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = feature.description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

data class InfoItem(
    val label: String,
    val value: String
)

data class FeatureItem(
    val icon: ImageVector,
    val title: String,
    val description: String
)
