package com.downloader.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\u001ap\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u000e\b\u0002\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0003\u001a\u0015\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0003\u00a2\u0006\u0002\u0010\u0015\u001a\u0010\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0002\u001a\u00020\u0003H\u0002\u00a8\u0006\u0018"}, d2 = {"DownloadCard", "", "downloadItem", "Lcom/downloader/app/data/model/DownloadItem;", "onPause", "Lkotlin/Function0;", "onResume", "onCancel", "onRetry", "onDelete", "onPreview", "modifier", "Landroidx/compose/ui/Modifier;", "getFileTypeIcon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "fileType", "Lcom/downloader/app/data/model/FileType;", "getStatusColor", "Landroidx/compose/ui/graphics/Color;", "status", "Lcom/downloader/app/data/model/DownloadStatus;", "(Lcom/downloader/app/data/model/DownloadStatus;)J", "getStatusText", "", "app_debug"})
public final class DownloadCardKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DownloadCard(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem downloadItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPause, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onResume, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDelete, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPreview, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final androidx.compose.ui.graphics.vector.ImageVector getFileTypeIcon(com.downloader.app.data.model.FileType fileType) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    private static final long getStatusColor(com.downloader.app.data.model.DownloadStatus status) {
        return 0L;
    }
    
    private static final java.lang.String getStatusText(com.downloader.app.data.model.DownloadItem downloadItem) {
        return null;
    }
}