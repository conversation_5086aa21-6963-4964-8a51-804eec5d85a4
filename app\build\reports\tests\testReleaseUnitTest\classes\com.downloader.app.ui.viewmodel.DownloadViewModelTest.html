<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.downloader.app.ui.viewmodel.DownloadViewModelTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.downloader.app.ui.viewmodel.DownloadViewModelTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.downloader.app.ui.viewmodel.html">com.downloader.app.ui.viewmodel</a> &gt; DownloadViewModelTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">11</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">5</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.619s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">54%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="clearMessage should clear success and error messages"></a>
<h3 class="failures">clearMessage should clear success and error messages</h3>
<span class="code">
<pre>expected an empty string
but was: null
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1$1.invokeSuspend(DownloadViewModelTest.kt:208)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1$1.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1.invokeSuspend(DownloadViewModelTest.kt:206)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$clearMessage should clear success and error messages$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.clearMessage should clear success and error messages(DownloadViewModelTest.kt:202)
</pre>
</span>
</div>
<div class="test">
<a name="initial state should be correct"></a>
<h3 class="failures">initial state should be correct</h3>
<span class="code">
<pre>expected an empty string
but was: null
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1$1.invokeSuspend(DownloadViewModelTest.kt:65)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1$1.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1.invokeSuspend(DownloadViewModelTest.kt:59)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$initial state should be correct$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.initial state should be correct(DownloadViewModelTest.kt:58)
</pre>
</span>
</div>
<div class="test">
<a name="startDownload should handle Instagram URL with not supported message"></a>
<h3 class="failures">startDownload should handle Instagram URL with not supported message</h3>
<span class="code">
<pre>expected to contain: Instagram downloads are not yet supported
but was            : Extracting Instagram content...please wait
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1$1.invokeSuspend(DownloadViewModelTest.kt:151)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1$1.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1.invokeSuspend(DownloadViewModelTest.kt:149)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle Instagram URL with not supported message$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.startDownload should handle Instagram URL with not supported message(DownloadViewModelTest.kt:142)
</pre>
</span>
</div>
<div class="test">
<a name="startDownload should handle YouTube URL"></a>
<h3 class="failures">startDownload should handle YouTube URL</h3>
<span class="code">
<pre>expected to contain: YouTube video extracted successfully
but was            : Extracting YouTube video...please wait
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1$3.invokeSuspend(DownloadViewModelTest.kt:120)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1$3.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1$3.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1.invokeSuspend(DownloadViewModelTest.kt:118)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle YouTube URL$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.startDownload should handle YouTube URL(DownloadViewModelTest.kt:98)
</pre>
</span>
</div>
<div class="test">
<a name="startDownload should handle direct file URL"></a>
<h3 class="failures">startDownload should handle direct file URL</h3>
<span class="code">
<pre>expected to be empty
but was: Method getExternalStoragePublicDirectory in android.os.Environment not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1$1.invokeSuspend(DownloadViewModelTest.kt:167)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1$1.invoke(DownloadViewModelTest.kt)
	at app//app.cash.turbine.FlowKt$test$2.invokeSuspend(flow.kt:149)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$test$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invokeSuspend(flow.kt:91)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$1.invoke(flow.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.CoroutineScopeKt.coroutineScope(CoroutineScope.kt:261)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invokeSuspend(flow.kt:83)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2$scopeFn$1.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invokeSuspend(flow.kt:88)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.FlowKt$turbineScope$2.invoke(flow.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invokeSuspend(coroutines.kt:89)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//app.cash.turbine.CoroutinesKt$reportTurbines$2.invoke(coroutines.kt)
	at app//kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:61)
	at app//kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:163)
	at app//kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
	at app//app.cash.turbine.CoroutinesKt.reportTurbines(coroutines.kt:88)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0(flow.kt:80)
	at app//app.cash.turbine.FlowKt.turbineScope-k1IrOU0$default(flow.kt:75)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE(flow.kt:141)
	at app//app.cash.turbine.FlowKt.test-C2H2yOE$default(flow.kt:136)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1.invokeSuspend(DownloadViewModelTest.kt:165)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1.invoke(DownloadViewModelTest.kt)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest$startDownload should handle direct file URL$1.invoke(DownloadViewModelTest.kt)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.test.TestDispatcher.processEvent$kotlinx_coroutines_test(TestDispatcher.kt:24)
	at app//kotlinx.coroutines.test.TestCoroutineScheduler.tryRunNextTaskUnless$kotlinx_coroutines_test(TestCoroutineScheduler.kt:99)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$workRunner$1.invokeSuspend(TestBuilders.kt:322)
	at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:277)
	at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
	at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
	at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:48)
	at app//kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersJvmKt.createTestResult(TestBuildersJvm.kt:10)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:310)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0(TestBuilders.kt:168)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0(Unknown Source)
	at app//kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt.runTest-8Mi8wO0$default(TestBuilders.kt:160)
	at app//kotlinx.coroutines.test.TestBuildersKt.runTest-8Mi8wO0$default(Unknown Source)
	at app//com.downloader.app.ui.viewmodel.DownloadViewModelTest.startDownload should handle direct file URL(DownloadViewModelTest.kt:156)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">clearHistory should clear downloads list</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">clearMessage should clear success and error messages</td>
<td class="failures">0.018s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">dismissClipboardDialog should clear clipboard state</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">initial state should be correct</td>
<td class="failures">0.017s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">startDownload should handle Instagram URL with not supported message</td>
<td class="failures">0.028s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">startDownload should handle YouTube URL</td>
<td class="failures">0.038s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">startDownload should handle YouTube extraction failure</td>
<td class="success">0.470s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">startDownload should handle direct file URL</td>
<td class="failures">0.018s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">startDownload should show error for invalid URL</td>
<td class="success">0.006s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">updateUrl should update current URL in state</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">useClipboardUrl should update current URL from clipboard</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.12</a> at Jul 27, 2025, 11:34:50 PM</p>
</div>
</div>
</body>
</html>
