package com.downloader.app

import android.content.Context
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import app.cash.turbine.test
import com.downloader.app.service.InstagramExtractorService
import com.downloader.app.service.YouTubeExtractorService
import com.downloader.app.ui.viewmodel.DownloadViewModel
import com.downloader.app.utils.ClipboardHelper
import com.downloader.app.utils.FileUtils
import com.google.common.truth.Truth.assertThat
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class)
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class DownloadIntegrationTest {

    @get:Rule
    val hiltRule = HiltAndroidRule(this)

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()
    
    private lateinit var context: Context
    private lateinit var clipboardHelper: ClipboardHelper
    private lateinit var youTubeExtractorService: YouTubeExtractorService
    private lateinit var instagramExtractorService: InstagramExtractorService
    private lateinit var viewModel: DownloadViewModel

    @Before
    fun setup() {
        hiltRule.inject()
        Dispatchers.setMain(testDispatcher)
        
        context = ApplicationProvider.getApplicationContext()
        
        // Mock dependencies
        clipboardHelper = mockk(relaxed = true)
        youTubeExtractorService = mockk(relaxed = true)
        instagramExtractorService = mockk(relaxed = true)
        
        // Mock clipboard monitoring
        every { clipboardHelper.monitorClipboard() } returns flowOf()
        
        viewModel = DownloadViewModel(
            clipboardHelper,
            youTubeExtractorService,
            instagramExtractorService,
            context
        )
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    @Test
    fun testCompleteDownloadFlow_DirectFileUrl() = runTest {
        // Given
        val directUrl = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        
        // When
        viewModel.updateUrl(directUrl)
        viewModel.startDownload()
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Then
        viewModel.uiState.test {
            val state = awaitItem()
            
            // Should not show error for valid direct URL
            assertThat(state.errorMessage).isEmpty()
            assertThat(state.currentUrl).isEqualTo(directUrl)
        }
    }

    @Test
    fun testCompleteDownloadFlow_YouTubeUrl() = runTest {
        // Given
        val youtubeUrl = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        
        // When
        viewModel.updateUrl(youtubeUrl)
        viewModel.startDownload()
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Then
        viewModel.uiState.test {
            val state = awaitItem()
            
            // Should attempt YouTube extraction
            assertThat(state.currentUrl).isEqualTo(youtubeUrl)
            // The actual result depends on the YouTube extractor mock
        }
    }

    @Test
    fun testCompleteDownloadFlow_InstagramUrl() = runTest {
        // Given
        val instagramUrl = "https://www.instagram.com/p/ABC123/"
        
        // When
        viewModel.updateUrl(instagramUrl)
        viewModel.startDownload()
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Then
        viewModel.uiState.test {
            val state = awaitItem()
            
            // Should show Instagram not fully supported message
            assertThat(state.currentUrl).isEqualTo(instagramUrl)
            assertThat(state.errorMessage).contains("not yet fully supported")
        }
    }

    @Test
    fun testUrlValidation_InvalidUrls() = runTest {
        val invalidUrls = listOf(
            "",
            "not-a-url",
            "ftp://example.com/file.txt",
            "javascript:alert('test')"
        )
        
        invalidUrls.forEach { url ->
            // When
            viewModel.updateUrl(url)
            viewModel.startDownload()
            testDispatcher.scheduler.advanceUntilIdle()
            
            // Then
            viewModel.uiState.test {
                val state = awaitItem()
                assertThat(state.errorMessage).isEqualTo("Invalid URL")
            }
            
            // Clear error for next test
            viewModel.clearMessage()
            testDispatcher.scheduler.advanceUntilIdle()
        }
    }

    @Test
    fun testUrlValidation_ValidUrls() = runTest {
        val validUrls = listOf(
            "https://example.com/file.mp4",
            "http://example.com/image.jpg",
            "https://www.youtube.com/watch?v=123",
            "https://instagram.com/p/ABC123/",
            "example.com/document.pdf"
        )
        
        validUrls.forEach { url ->
            // When
            viewModel.updateUrl(url)
            
            // Then - should not show invalid URL error immediately
            viewModel.uiState.test {
                val state = awaitItem()
                assertThat(state.currentUrl).isEqualTo(url)
                assertThat(state.errorMessage).doesNotContain("Invalid URL")
            }
        }
    }

    @Test
    fun testFileUtilsIntegration() {
        // Test file name extraction
        val testCases = mapOf(
            "https://example.com/video.mp4" to "video.mp4",
            "https://example.com/path/to/file.jpg?param=value" to "file.jpg",
            "https://example.com/document.pdf#page=1" to "document.pdf"
        )
        
        testCases.forEach { (url, expectedFileName) ->
            val actualFileName = FileUtils.getFileNameFromUrl(url)
            assertThat(actualFileName).isEqualTo(expectedFileName)
        }
        
        // Test MIME type detection
        val mimeTestCases = mapOf(
            "https://example.com/video.mp4" to "video/mp4",
            "https://example.com/audio.mp3" to "audio/mpeg",
            "https://example.com/image.jpg" to "image/jpeg"
        )
        
        mimeTestCases.forEach { (url, expectedMimeType) ->
            val actualMimeType = FileUtils.getMimeTypeFromUrl(url)
            assertThat(actualMimeType).isEqualTo(expectedMimeType)
        }
    }

    @Test
    fun testClipboardIntegration() = runTest {
        // Test clipboard URL detection
        val clipboardUrl = "https://example.com/clipboard-video.mp4"
        
        // Simulate clipboard URL being detected
        every { clipboardHelper.monitorClipboard() } returns flowOf(clipboardUrl)
        
        // Create new view model to trigger clipboard monitoring
        val newViewModel = DownloadViewModel(
            clipboardHelper,
            youTubeExtractorService,
            instagramExtractorService,
            context
        )
        
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Should detect clipboard URL
        newViewModel.uiState.test {
            val state = awaitItem()
            // The clipboard monitoring would update the state
            // This test would need more complex setup to fully verify
        }
    }

    @Test
    fun testErrorHandling_NetworkErrors() = runTest {
        // Test various error scenarios
        val testUrl = "https://example.com/video.mp4"
        
        viewModel.updateUrl(testUrl)
        viewModel.startDownload()
        testDispatcher.scheduler.advanceUntilIdle()
        
        // The actual error handling would depend on the download service
        // This test verifies the basic flow doesn't crash
        viewModel.uiState.test {
            val state = awaitItem()
            assertThat(state.currentUrl).isEqualTo(testUrl)
        }
    }

    @Test
    fun testStateManagement() = runTest {
        // Test state transitions
        val testUrl = "https://example.com/test.mp4"
        
        // Initial state
        viewModel.uiState.test {
            val initialState = awaitItem()
            assertThat(initialState.currentUrl).isEmpty()
            assertThat(initialState.errorMessage).isEmpty()
            assertThat(initialState.successMessage).isEmpty()
        }
        
        // Update URL
        viewModel.updateUrl(testUrl)
        testDispatcher.scheduler.advanceUntilIdle()
        
        viewModel.uiState.test {
            val state = awaitItem()
            assertThat(state.currentUrl).isEqualTo(testUrl)
        }
        
        // Clear messages
        viewModel.clearMessage()
        testDispatcher.scheduler.advanceUntilIdle()
        
        viewModel.uiState.test {
            val state = awaitItem()
            assertThat(state.errorMessage).isEmpty()
            assertThat(state.successMessage).isEmpty()
        }
    }
}
