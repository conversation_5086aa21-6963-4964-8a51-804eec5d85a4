package com.downloader.app.ui.screens

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Download
import androidx.compose.material.icons.filled.Link
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.ui.components.DownloadCard
import com.downloader.app.ui.components.FilePreviewDialog
import com.downloader.app.ui.viewmodel.DownloadViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DownloadScreen(
    viewModel: DownloadViewModel,
    onNavigateToAbout: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val downloads by viewModel.downloads.collectAsStateWithLifecycle()
    var selectedDownloadForPreview by remember { mutableStateOf<DownloadItem?>(null) }
    val snackbarHostState = remember { SnackbarHostState() }

    // Show messages
    LaunchedEffect(uiState.errorMessage) {
        uiState.errorMessage?.let {
            snackbarHostState.showSnackbar(it, duration = SnackbarDuration.Short)
            viewModel.clearMessage()
        }
    }

    LaunchedEffect(uiState.successMessage) {
        uiState.successMessage?.let {
            snackbarHostState.showSnackbar(it, duration = SnackbarDuration.Short)
            viewModel.clearMessage()
        }
    }

    // Show clipboard dialog
    if (uiState.showClipboardDialog) {
        ClipboardDialog(
            clipboardUrl = uiState.clipboardUrl,
            onDismiss = { viewModel.dismissClipboardDialog() },
            onConfirm = { viewModel.useClipboardUrl() }
        )
    }

    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            TopAppBar(
                title = { Text("Dawn", fontWeight = FontWeight.Bold) },
                actions = {
                    IconButton(onClick = onNavigateToAbout) {
                        Icon(Icons.Default.Info, contentDescription = "About")
                    }
                }
            )
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            // URL Input Section
            UrlInputSection(
                url = uiState.currentUrl,
                onUrlChange = viewModel::updateUrl,
                onDownload = { viewModel.startDownload() }
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Downloads Section
            DownloadsSection(
                downloads = downloads,
                onClearHistory = { viewModel.clearHistory() },
                onPause = { viewModel.pauseDownload(it.id) },
                onResume = { viewModel.resumeDownload(it.id) },
                onCancel = { viewModel.cancelDownload(it.id) },
                onRetry = { viewModel.retryDownload(it) },
                onDelete = { viewModel.deleteDownload(it) },
                onPreview = { selectedDownloadForPreview = it }
            )
        }
    }

    // Preview Dialog
    selectedDownloadForPreview?.let { download ->
        FilePreviewDialog(
            downloadItem = download,
            onDismiss = { selectedDownloadForPreview = null }
        )
    }
}

@Composable
private fun ClipboardDialog(
    clipboardUrl: String,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("URL Detected") },
        text = {
            Column {
                Text("Found URL in clipboard:")
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = clipboardUrl,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        },
        confirmButton = {
            TextButton(onClick = onConfirm) {
                Text("Use URL")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Dismiss")
            }
        }
    )
}

@Composable
private fun UrlInputSection(
    url: String,
    onUrlChange: (String) -> Unit,
    onDownload: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            OutlinedTextField(
                value = url,
                onValueChange = onUrlChange,
                label = { Text("Paste URL here") },
                placeholder = { Text("https://example.com/file.mp4") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                leadingIcon = { Icon(Icons.Default.Link, contentDescription = null) },
                trailingIcon = {
                    if (url.isNotEmpty()) {
                        IconButton(onClick = { onUrlChange("") }) {
                            Icon(Icons.Default.Clear, contentDescription = "Clear")
                        }
                    }
                }
            )

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = onDownload,
                modifier = Modifier.fillMaxWidth(),
                enabled = url.isNotBlank()
            ) {
                Icon(Icons.Default.Download, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Download")
            }
        }
    }
}

@Composable
private fun DownloadsSection(
    downloads: List<DownloadItem>,
    onClearHistory: () -> Unit,
    onPause: (DownloadItem) -> Unit,
    onResume: (DownloadItem) -> Unit,
    onCancel: (DownloadItem) -> Unit,
    onRetry: (DownloadItem) -> Unit,
    onDelete: (DownloadItem) -> Unit,
    onPreview: (DownloadItem) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "Downloads",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Medium
        )

        if (downloads.isNotEmpty()) {
            TextButton(onClick = onClearHistory) {
                Text("Clear All")
            }
        }
    }

    Spacer(modifier = Modifier.height(16.dp))

    if (downloads.isEmpty()) {
        EmptyDownloadsView()
    } else {
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(downloads) { download ->
                DownloadCard(
                    downloadItem = download,
                    onPause = { onPause(download) },
                    onResume = { onResume(download) },
                    onCancel = { onCancel(download) },
                    onRetry = { onRetry(download) },
                    onDelete = { onDelete(download) },
                    onPreview = { onPreview(download) }
                )
            }
        }
    }
}

@Composable
private fun EmptyDownloadsView() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                Icons.Default.Download,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.outline
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "No downloads yet",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.outline
            )
            Text(
                text = "Paste a URL above to start downloading",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.outline
            )
        }
    }
}

