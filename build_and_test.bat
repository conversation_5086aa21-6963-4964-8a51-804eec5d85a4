@echo off
echo Building Universal Downloader Android App...
echo.

echo Step 1: Cleaning project...
call gradlew clean

echo.
echo Step 2: Building debug APK...
call gradlew assembleDebug

echo.
echo Step 3: Running lint checks...
call gradlew lint

echo.
echo Build completed! Check the following:
echo - APK location: app/build/outputs/apk/debug/
echo - Lint report: app/build/reports/lint-results.html
echo.
pause