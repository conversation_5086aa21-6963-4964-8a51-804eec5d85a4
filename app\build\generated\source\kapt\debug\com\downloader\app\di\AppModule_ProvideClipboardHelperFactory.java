package com.downloader.app.di;

import android.content.Context;
import com.downloader.app.utils.ClipboardHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideClipboardHelperFactory implements Factory<ClipboardHelper> {
  private final Provider<Context> contextProvider;

  public AppModule_ProvideClipboardHelperFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ClipboardHelper get() {
    return provideClipboardHelper(contextProvider.get());
  }

  public static AppModule_ProvideClipboardHelperFactory create(Provider<Context> contextProvider) {
    return new AppModule_ProvideClipboardHelperFactory(contextProvider);
  }

  public static ClipboardHelper provideClipboardHelper(Context context) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideClipboardHelper(context));
  }
}
