package com.downloader.app.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0007\u0018\u00002\u00020\u0001B)\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0001\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aJ\u0006\u0010\u001b\u001a\u00020\u0018J\u0006\u0010\u001c\u001a\u00020\u0018J\u000e\u0010\u001d\u001a\u00020\u00182\u0006\u0010\u001e\u001a\u00020\u0010J\u0006\u0010\u001f\u001a\u00020\u0018J\u0010\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020#H\u0002J\u0010\u0010$\u001a\u00020!2\u0006\u0010\"\u001a\u00020#H\u0002J\b\u0010%\u001a\u00020\u0018H\u0002J\u000e\u0010&\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aJ\u000e\u0010'\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aJ\u000e\u0010(\u001a\u00020\u00182\u0006\u0010\u001e\u001a\u00020\u0010J\u0006\u0010)\u001a\u00020\u0018J\u0010\u0010*\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020#H\u0002J\u0010\u0010+\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020#H\u0002J\u000e\u0010,\u001a\u00020\u00182\u0006\u0010\"\u001a\u00020#J\u0006\u0010-\u001a\u00020\u0018R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u000f0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\r0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006."}, d2 = {"Lcom/downloader/app/ui/viewmodel/DownloadViewModel;", "Landroidx/lifecycle/ViewModel;", "clipboardHelper", "Lcom/downloader/app/utils/ClipboardHelper;", "youTubeExtractorService", "Lcom/downloader/app/service/YouTubeExtractorService;", "instagramExtractorService", "Lcom/downloader/app/service/InstagramExtractorService;", "context", "Landroid/content/Context;", "(Lcom/downloader/app/utils/ClipboardHelper;Lcom/downloader/app/service/YouTubeExtractorService;Lcom/downloader/app/service/InstagramExtractorService;Landroid/content/Context;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/downloader/app/ui/viewmodel/DownloadUiState;", "downloads", "", "Lcom/downloader/app/data/model/DownloadItem;", "getDownloads", "()Lkotlinx/coroutines/flow/MutableStateFlow;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "cancelDownload", "", "id", "", "clearHistory", "clearMessage", "deleteDownload", "item", "dismissClipboardDialog", "isInstagramUrl", "", "url", "", "isYouTubeUrl", "monitorClipboard", "pauseDownload", "resumeDownload", "retryDownload", "startDownload", "startInstagramDownload", "startYouTubeDownload", "updateUrl", "useClipboardUrl", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class DownloadViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.downloader.app.utils.ClipboardHelper clipboardHelper = null;
    @org.jetbrains.annotations.NotNull()
    private final com.downloader.app.service.YouTubeExtractorService youTubeExtractorService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.downloader.app.service.InstagramExtractorService instagramExtractorService = null;
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.downloader.app.ui.viewmodel.DownloadUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.downloader.app.ui.viewmodel.DownloadUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.downloader.app.data.model.DownloadItem>> downloads = null;
    
    @javax.inject.Inject()
    public DownloadViewModel(@org.jetbrains.annotations.NotNull()
    com.downloader.app.utils.ClipboardHelper clipboardHelper, @org.jetbrains.annotations.NotNull()
    com.downloader.app.service.YouTubeExtractorService youTubeExtractorService, @org.jetbrains.annotations.NotNull()
    com.downloader.app.service.InstagramExtractorService instagramExtractorService, @dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.downloader.app.ui.viewmodel.DownloadUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.downloader.app.data.model.DownloadItem>> getDownloads() {
        return null;
    }
    
    private final void monitorClipboard() {
    }
    
    private final boolean isYouTubeUrl(java.lang.String url) {
        return false;
    }
    
    private final boolean isInstagramUrl(java.lang.String url) {
        return false;
    }
    
    private final void startYouTubeDownload(java.lang.String url) {
    }
    
    private final void startInstagramDownload(java.lang.String url) {
    }
    
    public final void updateUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
    }
    
    public final void useClipboardUrl() {
    }
    
    public final void dismissClipboardDialog() {
    }
    
    public final void startDownload() {
    }
    
    public final void clearMessage() {
    }
    
    public final void clearHistory() {
    }
    
    public final void pauseDownload(long id) {
    }
    
    public final void resumeDownload(long id) {
    }
    
    public final void cancelDownload(long id) {
    }
    
    public final void retryDownload(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem item) {
    }
    
    public final void deleteDownload(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem item) {
    }
}