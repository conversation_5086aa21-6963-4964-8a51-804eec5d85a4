package com.downloader.app.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000>\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\u001a \u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a\b\u0010\u0006\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0007\u001a\u00020\u0001H\u0003\u001a+\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0011\u0010\r\u001a\r\u0012\u0004\u0012\u00020\u00010\u0003\u00a2\u0006\u0002\b\u000eH\u0003\u001a\u0010\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u0011H\u0003\u001a\b\u0010\u0012\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0013\u001a\u00020\u0001H\u0003\u001a\b\u0010\u0014\u001a\u00020\u0001H\u0003\u001a\u0016\u0010\u0015\u001a\u00020\u00012\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00180\u0017H\u0003\u001a\b\u0010\u0019\u001a\u00020\u0001H\u0003\u001a\b\u0010\u001a\u001a\u00020\u0001H\u0003\u00a8\u0006\u001b"}, d2 = {"AboutScreen", "", "onNavigateBack", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "AppHeader", "DeveloperSectionContent", "ExpandableCard", "title", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "content", "Landroidx/compose/runtime/Composable;", "FeatureRow", "feature", "Lcom/downloader/app/ui/screens/FeatureItem;", "FeaturesSectionContent", "FileSupportSectionContent", "Footer", "InfoSectionContent", "items", "", "Lcom/downloader/app/ui/screens/InfoItem;", "PrivacySectionContent", "TechnicalSectionContent", "app_debug"})
public final class AboutScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AboutScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AppHeader() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void Footer() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ExpandableCard(java.lang.String title, androidx.compose.ui.graphics.vector.ImageVector icon, kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void InfoSectionContent(java.util.List<com.downloader.app.ui.screens.InfoItem> items) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FeaturesSectionContent() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FileSupportSectionContent() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TechnicalSectionContent() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PrivacySectionContent() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DeveloperSectionContent() {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FeatureRow(com.downloader.app.ui.screens.FeatureItem feature) {
    }
}