package com.downloader.app.data.database

import androidx.room.*
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.data.model.DownloadStatus
import kotlinx.coroutines.flow.Flow

@Dao
interface DownloadDao {
    @Query("SELECT * FROM DownloadItem")
    fun getAllDownloads(): Flow<List<DownloadItem>>

    @Query("SELECT * FROM DownloadItem WHERE id = :id")
    suspend fun getDownloadById(id: Long): DownloadItem?

    @Query("SELECT * FROM DownloadItem WHERE status = :status")
    suspend fun getDownloadsByStatus(status: DownloadStatus): List<DownloadItem>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDownload(download: DownloadItem): Long

    @Update
    suspend fun updateDownload(download: DownloadItem)

    @Query("UPDATE DownloadItem SET status = :status, progress = :progress, downloadedSize = :downloadedSize, downloadSpeed = :speed WHERE id = :id")
    suspend fun updateDownloadProgress(id: Long, status: DownloadStatus, progress: Int, downloadedSize: Long, speed: Long)

    @Query("UPDATE DownloadItem SET status = :status, completedAt = :completedAt WHERE id = :id")
    suspend fun updateDownloadStatus(id: Long, status: DownloadStatus, completedAt: Long?)

    @Query("UPDATE DownloadItem SET status = :status, errorMessage = :errorMessage WHERE id = :id")
    suspend fun updateDownloadError(id: Long, status: DownloadStatus, errorMessage: String)

    @Delete
    suspend fun deleteDownload(download: DownloadItem)

    @Query("DELETE FROM DownloadItem")
    suspend fun clearAllDownloads()

    @Query("DELETE FROM DownloadItem WHERE status = :status")
    suspend fun deleteDownloadsByStatus(status: DownloadStatus)
}
