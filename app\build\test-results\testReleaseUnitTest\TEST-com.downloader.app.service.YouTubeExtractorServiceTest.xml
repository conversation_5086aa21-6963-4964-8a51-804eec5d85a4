<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.downloader.app.service.YouTubeExtractorServiceTest" tests="11" skipped="0" failures="4" errors="0" timestamp="2025-07-27T18:04:49" hostname="ADDY" time="0.131">
  <properties/>
  <testcase name="normalizeYouTubeUrl should handle YouTube Music URLs" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.002"/>
  <testcase name="extractYouTubeUrl should normalize URL before extraction" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.007">
    <failure message="java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details." type="java.lang.RuntimeException">java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at _COROUTINE._BOUNDARY._(CoroutineDebugging.kt:42)
	at com.downloader.app.service.YouTubeExtractorService.extractYouTubeUrl-gIAlu-s(YouTubeExtractorService.kt:33)
	at com.downloader.app.service.YouTubeExtractorServiceTest$extractYouTubeUrl should normalize URL before extraction$1.invokeSuspend(YouTubeExtractorServiceTest.kt:172)
	at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
Caused by: java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
</failure>
  </testcase>
  <testcase name="normalizeYouTubeUrl should handle youtu_be URLs with parameters" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.0"/>
  <testcase name="normalizeYouTubeUrl should return original URL for standard YouTube URLs" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.001"/>
  <testcase name="extractYouTubeUrl should validate input URL format" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.003">
    <failure message="java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details." type="java.lang.RuntimeException">java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at _COROUTINE._BOUNDARY._(CoroutineDebugging.kt:42)
	at com.downloader.app.service.YouTubeExtractorService.extractYouTubeUrl-gIAlu-s(YouTubeExtractorService.kt:33)
	at com.downloader.app.service.YouTubeExtractorServiceTest$extractYouTubeUrl should validate input URL format$1.invokeSuspend(YouTubeExtractorServiceTest.kt:158)
	at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
Caused by: java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
</failure>
  </testcase>
  <testcase name="normalizeYouTubeUrl should handle youtu_be short URLs" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.001"/>
  <testcase name="normalizeYouTubeUrl should handle YouTube Shorts URLs with parameters" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.001"/>
  <testcase name="normalizeYouTubeUrl should handle mobile YouTube URLs" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.001"/>
  <testcase name="normalizeYouTubeUrl should handle YouTube Shorts URLs" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.001"/>
  <testcase name="extractYouTubeUrl should handle timeout gracefully" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.004">
    <failure message="java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details." type="java.lang.RuntimeException">java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at _COROUTINE._BOUNDARY._(CoroutineDebugging.kt:42)
	at com.downloader.app.service.YouTubeExtractorService.extractYouTubeUrl-gIAlu-s(YouTubeExtractorService.kt:33)
	at com.downloader.app.service.YouTubeExtractorServiceTest$extractYouTubeUrl should handle timeout gracefully$1.invokeSuspend(YouTubeExtractorServiceTest.kt:141)
	at kotlinx.coroutines.test.TestBuildersKt__TestBuildersKt$runTest$2$1$1.invokeSuspend(TestBuilders.kt:316)
Caused by: java.lang.RuntimeException: Method e in android.util.Log not mocked. See https://developer.android.com/r/studio-ui/build/not-mocked for details.
	at android.util.Log.e(Log.java)
	at com.downloader.app.service.YouTubeExtractorService$extractYouTubeUrl$2.invokeSuspend(YouTubeExtractorService.kt:138)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
</failure>
  </testcase>
  <testcase name="normalizeYouTubeUrl should handle edge cases" classname="com.downloader.app.service.YouTubeExtractorServiceTest" time="0.11">
    <failure message="expected: https://youtu.be/&#10;but was : https://www.youtube.com/watch?v=" type="com.google.common.truth.ComparisonFailureWithFacts">expected: https://youtu.be/
but was : https://www.youtube.com/watch?v=
	at app//com.downloader.app.service.YouTubeExtractorServiceTest.normalizeYouTubeUrl should handle edge cases(YouTubeExtractorServiceTest.kt:131)
</failure>
  </testcase>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
