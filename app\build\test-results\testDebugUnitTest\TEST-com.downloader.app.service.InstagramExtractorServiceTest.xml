<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.downloader.app.service.InstagramExtractorServiceTest" tests="14" skipped="0" failures="0" errors="0" timestamp="2025-07-27T18:12:13" hostname="ADDY" time="0.357">
  <properties/>
  <testcase name="extractContentId should extract ID from post URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.31"/>
  <testcase name="extractContentId should extract ID from reel URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.004"/>
  <testcase name="getInstagramContentType should identify post URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.005"/>
  <testcase name="extractContentId should return empty string for invalid URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.002"/>
  <testcase name="getInstagramContentType should identify reel URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.003"/>
  <testcase name="normalizeInstagramUrl should add HTTPS protocol" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.004"/>
  <testcase name="getInstagramContentType should identify TV URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.002"/>
  <testcase name="extractContentId should extract ID from TV URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.003"/>
  <testcase name="normalizeInstagramUrl should handle instagr_am short URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.003"/>
  <testcase name="extractContentId should extract ID from story URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.003"/>
  <testcase name="getInstagramContentType should return UNKNOWN for invalid URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.003"/>
  <testcase name="normalizeInstagramUrl should remove trailing slash" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.001"/>
  <testcase name="getInstagramContentType should identify story URLs" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.003"/>
  <testcase name="extractInstagramUrl should return failure for current implementation" classname="com.downloader.app.service.InstagramExtractorServiceTest" time="0.009"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
