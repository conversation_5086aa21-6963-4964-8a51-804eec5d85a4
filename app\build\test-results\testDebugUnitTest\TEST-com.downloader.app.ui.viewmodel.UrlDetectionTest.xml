<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.downloader.app.ui.viewmodel.UrlDetectionTest" tests="6" skipped="0" failures="0" errors="0" timestamp="2025-07-27T18:12:14" hostname="ADDY" time="0.005">
  <properties/>
  <testcase name="should be case insensitive for URL detection" classname="com.downloader.app.ui.viewmodel.UrlDetectionTest" time="0.001"/>
  <testcase name="should not detect non-Instagram URLs as Instagram" classname="com.downloader.app.ui.viewmodel.UrlDetectionTest" time="0.001"/>
  <testcase name="should detect Instagram URLs correctly" classname="com.downloader.app.ui.viewmodel.UrlDetectionTest" time="0.001"/>
  <testcase name="should not detect non-YouTube URLs as YouTube" classname="com.downloader.app.ui.viewmodel.UrlDetectionTest" time="0.0"/>
  <testcase name="should handle edge cases for URL detection" classname="com.downloader.app.ui.viewmodel.UrlDetectionTest" time="0.001"/>
  <testcase name="should detect YouTube URLs correctly" classname="com.downloader.app.ui.viewmodel.UrlDetectionTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
