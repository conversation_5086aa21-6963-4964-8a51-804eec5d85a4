<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.downloader.app.utils.FileUtilsTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.downloader.app.utils.FileUtilsTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.downloader.app.utils.html">com.downloader.app.utils</a> &gt; FileUtilsTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">17</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">3</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.040s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">82%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="getMimeTypeFromUrl should return correct mime type for audio"></a>
<h3 class="failures">getMimeTypeFromUrl should return correct mime type for audio</h3>
<span class="code">
<pre>value of: getMimeTypeFromUrl(...)
expected: audio/mpeg
but was : application/octet-stream
	at app//com.downloader.app.utils.FileUtilsTest.getMimeTypeFromUrl should return correct mime type for audio(FileUtilsTest.kt:89)
</pre>
</span>
</div>
<div class="test">
<a name="getMimeTypeFromUrl should return correct mime type for image"></a>
<h3 class="failures">getMimeTypeFromUrl should return correct mime type for image</h3>
<span class="code">
<pre>value of: getMimeTypeFromUrl(...)
expected: image/jpeg
but was : application/octet-stream
	at app//com.downloader.app.utils.FileUtilsTest.getMimeTypeFromUrl should return correct mime type for image(FileUtilsTest.kt:101)
</pre>
</span>
</div>
<div class="test">
<a name="getMimeTypeFromUrl should return correct mime type for video"></a>
<h3 class="failures">getMimeTypeFromUrl should return correct mime type for video</h3>
<span class="code">
<pre>value of: getMimeTypeFromUrl(...)
expected: video/mp4
but was : application/octet-stream
	at app//com.downloader.app.utils.FileUtilsTest.getMimeTypeFromUrl should return correct mime type for video(FileUtilsTest.kt:77)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">getFileNameFromUrl should extract filename from valid URL</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getFileNameFromUrl should generate default name for URL without extension</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getFileNameFromUrl should generate default name for URL without filename</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getFileNameFromUrl should handle URL with fragment</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">getFileNameFromUrl should handle URL with query parameters</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="failures">getMimeTypeFromUrl should return correct mime type for audio</td>
<td class="failures">0.015s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getMimeTypeFromUrl should return correct mime type for image</td>
<td class="failures">0.009s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="failures">getMimeTypeFromUrl should return correct mime type for video</td>
<td class="failures">0.009s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">getMimeTypeFromUrl should return default mime type for unknown extension</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isValidUrl should return false for empty string</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isValidUrl should return false for invalid URL</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isValidUrl should return true for URL without protocol</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isValidUrl should return true for valid HTTP URL</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">isValidUrl should return true for valid HTTPS URL</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">sanitizeFileName should handle multiple consecutive underscores</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">sanitizeFileName should replace invalid characters</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">sanitizeFileName should trim leading and trailing underscores</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.12</a> at Jul 27, 2025, 11:34:50 PM</p>
</div>
</div>
</body>
</html>
