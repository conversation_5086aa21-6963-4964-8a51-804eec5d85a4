package com.downloader.app.data.repository

import com.downloader.app.data.database.DownloadDao
import com.downloader.app.data.model.DownloadItem
import com.downloader.app.data.model.DownloadStatus
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DownloadRepository @Inject constructor(
    private val downloadDao: DownloadDao
) {
    
    fun getAllDownloads(): Flow<List<DownloadItem>> = downloadDao.getAllDownloads()
    
    suspend fun getDownloadById(id: Long): DownloadItem? = downloadDao.getDownloadById(id)
    
    suspend fun getDownloadsByStatus(status: DownloadStatus): List<DownloadItem> = 
        downloadDao.getDownloadsByStatus(status)
    
    suspend fun insertDownload(download: DownloadItem): Long = downloadDao.insertDownload(download)
    
    suspend fun updateDownload(download: DownloadItem) = downloadDao.updateDownload(download)
    
    suspend fun updateDownloadProgress(
        id: Long,
        status: DownloadStatus,
        progress: Int,
        downloadedSize: Long,
        speed: Long
    ) = downloadDao.updateDownloadProgress(id, status, progress, downloadedSize, speed)
    
    suspend fun updateDownloadStatus(id: Long, status: DownloadStatus, completedAt: Long? = null) = 
        downloadDao.updateDownloadStatus(id, status, completedAt)
    
    suspend fun updateDownloadError(id: Long, status: DownloadStatus, errorMessage: String) = 
        downloadDao.updateDownloadError(id, status, errorMessage)
    
    suspend fun deleteDownload(download: DownloadItem) = downloadDao.deleteDownload(download)
    
    suspend fun clearAllDownloads() = downloadDao.clearAllDownloads()
    
    suspend fun deleteDownloadsByStatus(status: DownloadStatus) = 
        downloadDao.deleteDownloadsByStatus(status)
}