package com.downloader.app.service;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 \u00132\u00020\u0001:\u0002\u0013\u0014B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\bH\u0002J$\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\r\u001a\u00020\bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u000e\u0010\u0010\u001a\u00020\u00112\u0006\u0010\t\u001a\u00020\bJ\u0010\u0010\u0012\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\bH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0015"}, d2 = {"Lcom/downloader/app/service/InstagramExtractorService;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "okHttpClient", "Lokhttp3/OkHttpClient;", "extractContentId", "", "url", "extractInstagramUrl", "Lkotlin/Result;", "Lcom/downloader/app/data/model/DownloadItem;", "instagramUrl", "extractInstagramUrl-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInstagramContentType", "Lcom/downloader/app/service/InstagramExtractorService$InstagramContentType;", "normalizeInstagramUrl", "Companion", "InstagramContentType", "app_release"})
public final class InstagramExtractorService {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "InstagramExtractor";
    private static final java.util.regex.Pattern POST_PATTERN = null;
    private static final java.util.regex.Pattern REEL_PATTERN = null;
    private static final java.util.regex.Pattern TV_PATTERN = null;
    private static final java.util.regex.Pattern STORY_PATTERN = null;
    @org.jetbrains.annotations.NotNull()
    private final okhttp3.OkHttpClient okHttpClient = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.downloader.app.service.InstagramExtractorService.Companion Companion = null;
    
    @javax.inject.Inject()
    public InstagramExtractorService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    private final java.lang.String normalizeInstagramUrl(java.lang.String url) {
        return null;
    }
    
    private final java.lang.String extractContentId(java.lang.String url) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.downloader.app.service.InstagramExtractorService.InstagramContentType getInstagramContentType(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0016\u0010\u0003\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0007\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082T\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n \u0005*\u0004\u0018\u00010\u00040\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/downloader/app/service/InstagramExtractorService$Companion;", "", "()V", "POST_PATTERN", "Ljava/util/regex/Pattern;", "kotlin.jvm.PlatformType", "REEL_PATTERN", "STORY_PATTERN", "TAG", "", "TV_PATTERN", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/downloader/app/service/InstagramExtractorService$InstagramContentType;", "", "(Ljava/lang/String;I)V", "POST", "REEL", "TV", "STORY", "UNKNOWN", "app_release"})
    public static enum InstagramContentType {
        /*public static final*/ POST /* = new POST() */,
        /*public static final*/ REEL /* = new REEL() */,
        /*public static final*/ TV /* = new TV() */,
        /*public static final*/ STORY /* = new STORY() */,
        /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
        
        InstagramContentType() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.downloader.app.service.InstagramExtractorService.InstagramContentType> getEntries() {
            return null;
        }
    }
}