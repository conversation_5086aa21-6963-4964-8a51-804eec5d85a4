package com.downloader.app.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0002\b\r\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\f\u0010\u0013\u001a\u00060\u0014j\u0002`\u0015H\u0007J\f\u0010\u0016\u001a\u00060\u0014j\u0002`\u0015H\u0007J\f\u0010\u0017\u001a\u00060\u0014j\u0002`\u0015H\u0007J\f\u0010\u0018\u001a\u00060\u0014j\u0002`\u0015H\u0007J\b\u0010\u0019\u001a\u00020\u0014H\u0007J\f\u0010\u001a\u001a\u00060\u0014j\u0002`\u0015H\u0007J\f\u0010\u001b\u001a\u00060\u0014j\u0002`\u0015H\u0007J\f\u0010\u001c\u001a\u00060\u0014j\u0002`\u0015H\u0007J\f\u0010\u001d\u001a\u00060\u0014j\u0002`\u0015H\u0007J\f\u0010\u001e\u001a\u00060\u0014j\u0002`\u0015H\u0007J\b\u0010\u001f\u001a\u00020\u0014H\u0007J\f\u0010 \u001a\u00060\u0014j\u0002`\u0015H\u0007J\f\u0010!\u001a\u00060\u0014j\u0002`\u0015H\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000R\u0013\u0010\t\u001a\u00020\n8G\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/downloader/app/ui/viewmodel/DownloadViewModelTest;", "", "()V", "clipboardHelper", "Lcom/downloader/app/utils/ClipboardHelper;", "context", "Landroid/content/Context;", "instagramExtractorService", "Lcom/downloader/app/service/InstagramExtractorService;", "instantTaskExecutorRule", "Landroidx/arch/core/executor/testing/InstantTaskExecutorRule;", "getInstantTaskExecutorRule", "()Landroidx/arch/core/executor/testing/InstantTaskExecutorRule;", "testDispatcher", "Lkotlinx/coroutines/test/TestDispatcher;", "viewModel", "Lcom/downloader/app/ui/viewmodel/DownloadViewModel;", "youTubeExtractorService", "Lcom/downloader/app/service/YouTubeExtractorService;", "clearHistory should clear downloads list", "", "Lkotlinx/coroutines/test/TestResult;", "clearMessage should clear success and error messages", "dismissClipboardDialog should clear clipboard state", "initial state should be correct", "setup", "startDownload should handle Instagram URL with not supported message", "startDownload should handle YouTube URL", "startDownload should handle YouTube extraction failure", "startDownload should handle direct file URL", "startDownload should show error for invalid URL", "tearDown", "updateUrl should update current URL in state", "useClipboardUrl should update current URL from clipboard", "app_debugUnitTest"})
@kotlin.OptIn(markerClass = {kotlinx.coroutines.ExperimentalCoroutinesApi.class})
public final class DownloadViewModelTest {
    @org.jetbrains.annotations.NotNull()
    private final androidx.arch.core.executor.testing.InstantTaskExecutorRule instantTaskExecutorRule = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.test.TestDispatcher testDispatcher = null;
    private com.downloader.app.utils.ClipboardHelper clipboardHelper;
    private com.downloader.app.service.YouTubeExtractorService youTubeExtractorService;
    private com.downloader.app.service.InstagramExtractorService instagramExtractorService;
    private android.content.Context context;
    private com.downloader.app.ui.viewmodel.DownloadViewModel viewModel;
    
    public DownloadViewModelTest() {
        super();
    }
    
    @org.junit.Rule()
    @org.jetbrains.annotations.NotNull()
    public final androidx.arch.core.executor.testing.InstantTaskExecutorRule getInstantTaskExecutorRule() {
        return null;
    }
    
    @org.junit.Before()
    public final void setup() {
    }
    
    @org.junit.After()
    public final void tearDown() {
    }
}