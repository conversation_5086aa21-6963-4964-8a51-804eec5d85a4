package com.downloader.app.service;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class InstagramExtractorService_Factory implements Factory<InstagramExtractorService> {
  private final Provider<Context> contextProvider;

  public InstagramExtractorService_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public InstagramExtractorService get() {
    return newInstance(contextProvider.get());
  }

  public static InstagramExtractorService_Factory create(Provider<Context> contextProvider) {
    return new InstagramExtractorService_Factory(contextProvider);
  }

  public static InstagramExtractorService newInstance(Context context) {
    return new InstagramExtractorService(context);
  }
}
