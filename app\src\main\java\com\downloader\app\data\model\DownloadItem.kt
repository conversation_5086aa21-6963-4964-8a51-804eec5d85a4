package com.downloader.app.data.model

import java.util.Date
data class DownloadItem(
    val id: Long = 0,
    val url: String,
    val fileName: String,
    val filePath: String,
    val fileSize: Long = 0,
    val downloadedSize: Long = 0,
    val mimeType: String = "",
    val status: DownloadStatus = DownloadStatus.PENDING,
    val progress: Int = 0,
    val downloadSpeed: Long = 0, // bytes per second
    val createdAt: Date = Date(),
    val completedAt: Date? = null,
    val errorMessage: String? = null,
    val originalUrl: String? = null // Store original YouTube URL
)

enum class DownloadStatus {
    PENDING,
    DOWNLOADING,
    PAUSED,
    COMPLETED,
    FAILED,
    CANCELLED
}

enum class FileType {
    VIDEO,
    AUDIO,
    IMAGE,
    DOCUMENT,
    ARCHIVE,
    APK,
    EBOOK,
    CODE,
    FONT,
    EXECUTABLE,
    PRESENTATION,
    SPREADSHEET,
    DATABASE,
    CAD,
    VECTOR,
    THREED_MODEL,
    OTHER;
    
    companion object {
        fun fromMimeType(mimeType: String): FileType {
            return when {
                // Video formats
                mimeType.startsWith("video/") -> VIDEO
                
                // Audio formats
                mimeType.startsWith("audio/") -> AUDIO
                
                // Image formats
                mimeType.startsWith("image/") -> IMAGE
                
                // Document formats
                mimeType.contains("pdf") || mimeType.contains("msword") || 
                mimeType.contains("wordprocessingml") || mimeType.contains("text/") ||
                mimeType.contains("rtf") || mimeType.contains("odt") -> DOCUMENT
                
                // Presentation formats
                mimeType.contains("presentation") || mimeType.contains("powerpoint") ||
                mimeType.contains("impress") -> PRESENTATION
                
                // Spreadsheet formats
                mimeType.contains("spreadsheet") || mimeType.contains("excel") ||
                mimeType.contains("calc") -> SPREADSHEET
                
                // Archive formats
                mimeType.contains("zip") || mimeType.contains("rar") || 
                mimeType.contains("tar") || mimeType.contains("7z") ||
                mimeType.contains("gzip") || mimeType.contains("compress") -> ARCHIVE
                
                // eBook formats
                mimeType.contains("epub") || mimeType.contains("mobi") ||
                mimeType.contains("kindle") -> EBOOK
                
                // Code/Script formats
                mimeType.contains("javascript") || mimeType.contains("json") ||
                mimeType.contains("xml") || mimeType.contains("html") ||
                mimeType.contains("css") -> CODE
                
                // Font formats
                mimeType.contains("font") || mimeType.contains("truetype") ||
                mimeType.contains("opentype") -> FONT
                
                // Database formats
                mimeType.contains("database") || mimeType.contains("sqlite") -> DATABASE
                
                // APK
                mimeType.contains("apk") || mimeType.contains("android") -> APK
                
                // Executable
                mimeType.contains("executable") || mimeType.contains("msdownload") -> EXECUTABLE
                
                else -> OTHER
            }
        }
        
        fun fromFileName(fileName: String): FileType {
            val extension = fileName.substringAfterLast('.', "").lowercase()
            return when (extension) {
                // Video formats
                "mp4", "avi", "mkv", "mov", "wmv", "flv", "webm", "m4v", 
                "3gp", "3g2", "asf", "divx", "f4v", "m2ts", "mts", "ogv",
                "rm", "rmvb", "ts", "vob", "xvid" -> VIDEO
                
                // Audio formats
                "mp3", "wav", "flac", "aac", "ogg", "wma", "m4a", "opus",
                "aiff", "au", "ra", "amr", "ac3", "dts", "ape", "mka",
                "oga", "spx", "tta", "wv" -> AUDIO
                
                // Image formats
                "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "tiff",
                "tif", "ico", "psd", "ai", "eps", "raw", "cr2", "nef",
                "arw", "dng", "heic", "heif", "avif", "jxl" -> IMAGE
                
                // Vector graphics
                "cdr", "wmf", "emf" -> VECTOR
                
                // Document formats
                "pdf", "doc", "docx", "txt", "rtf", "odt", "pages",
                "tex", "md", "rst", "asciidoc" -> DOCUMENT
                
                // Presentation formats
                "ppt", "pptx", "odp", "key", "pps", "ppsx" -> PRESENTATION
                
                // Spreadsheet formats
                "xls", "xlsx", "ods", "numbers", "csv", "tsv" -> SPREADSHEET
                
                // Archive formats
                "zip", "rar", "7z", "tar", "gz", "bz2", "xz", "lz", "lzma",
                "cab", "iso", "dmg", "pkg", "deb", "rpm", "msi" -> ARCHIVE
                
                // eBook formats
                "epub", "mobi", "azw", "azw3", "fb2", "lit", "pdb", "tcr" -> EBOOK
                
                // Code/Script formats
                "html", "htm", "css", "js", "json", "xml", "yaml", "yml",
                "php", "py", "java", "cpp", "c", "h", "cs", "rb", "go",
                "rs", "swift", "kt", "scala", "pl", "sh", "bat", "ps1",
                "sql", "r", "m", "vb", "pas", "asm", "lua", "dart" -> CODE
                
                // Font formats
                "ttf", "otf", "woff", "woff2", "eot", "fon", "pfb", "pfm" -> FONT
                
                // Database formats
                "db", "sqlite", "sqlite3", "mdb", "accdb", "dbf" -> DATABASE
                
                // CAD formats
                "dwg", "dxf", "step", "stp", "iges", "igs", "stl", "obj" -> CAD
                
                // 3D Model formats
                "3ds", "max", "blend", "fbx", "dae", "x3d", "ply", "off" -> THREED_MODEL
                
                // Executable formats
                "run", "bin" -> EXECUTABLE
                
                // APK
                "apk", "aab" -> APK
                
                else -> OTHER
            }
        }
        
        fun getDisplayName(fileType: FileType): String {
            return when (fileType) {
                VIDEO -> "Video"
                AUDIO -> "Audio"
                IMAGE -> "Image"
                DOCUMENT -> "Document"
                PRESENTATION -> "Presentation"
                SPREADSHEET -> "Spreadsheet"
                ARCHIVE -> "Archive"
                EBOOK -> "eBook"
                CODE -> "Code"
                FONT -> "Font"
                DATABASE -> "Database"
                CAD -> "CAD File"
                VECTOR -> "Vector Graphics"
                THREED_MODEL -> "3D Model"
                EXECUTABLE -> "Executable"
                APK -> "Android App"
                OTHER -> "File"
            }
        }
        
        fun getDescription(fileType: FileType): String {
            return when (fileType) {
                VIDEO -> "Video file that can be played with media players"
                AUDIO -> "Audio file for music or sound playback"
                IMAGE -> "Image file that can be viewed and edited"
                DOCUMENT -> "Text document for reading and editing"
                PRESENTATION -> "Presentation file for slideshows"
                SPREADSHEET -> "Spreadsheet for data analysis"
                ARCHIVE -> "Compressed archive containing multiple files"
                EBOOK -> "Electronic book for reading"
                CODE -> "Source code or script file"
                FONT -> "Font file for text rendering"
                DATABASE -> "Database file containing structured data"
                CAD -> "Computer-aided design file"
                VECTOR -> "Scalable vector graphics file"
                THREED_MODEL -> "3D model file for rendering"
                EXECUTABLE -> "Executable program file"
                APK -> "Android application package"
                OTHER -> "General file"
            }
        }
    }
}
