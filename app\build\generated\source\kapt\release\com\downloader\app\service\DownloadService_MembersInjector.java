package com.downloader.app.service;

import com.downloader.app.utils.NotificationHelper;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DownloadService_MembersInjector implements MembersInjector<DownloadService> {
  private final Provider<NotificationHelper> notificationHelperProvider;

  public DownloadService_MembersInjector(Provider<NotificationHelper> notificationHelperProvider) {
    this.notificationHelperProvider = notificationHelperProvider;
  }

  public static MembersInjector<DownloadService> create(
      Provider<NotificationHelper> notificationHelperProvider) {
    return new DownloadService_MembersInjector(notificationHelperProvider);
  }

  @Override
  public void injectMembers(DownloadService instance) {
    injectNotificationHelper(instance, notificationHelperProvider.get());
  }

  @InjectedFieldSignature("com.downloader.app.service.DownloadService.notificationHelper")
  public static void injectNotificationHelper(DownloadService instance,
      NotificationHelper notificationHelper) {
    instance.notificationHelper = notificationHelper;
  }
}
