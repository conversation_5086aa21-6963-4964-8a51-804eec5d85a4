<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class com.downloader.app.ui.viewmodel.UrlDetectionTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class com.downloader.app.ui.viewmodel.UrlDetectionTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.downloader.app.ui.viewmodel.html">com.downloader.app.ui.viewmodel</a> &gt; UrlDetectionTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">6</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">1</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.024s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">83%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Tests</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<div class="test">
<a name="should be case insensitive for URL detection"></a>
<h3 class="failures">should be case insensitive for URL detection</h3>
<span class="code">
<pre>expected to be true
	at app//com.downloader.app.ui.viewmodel.UrlDetectionTest.should be case insensitive for URL detection(UrlDetectionTest.kt:177)
</pre>
</span>
</div>
</div>
<div id="tab1" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="failures">should be case insensitive for URL detection</td>
<td class="failures">0.018s</td>
<td class="failures">failed</td>
</tr>
<tr>
<td class="success">should detect Instagram URLs correctly</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should detect YouTube URLs correctly</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should handle edge cases for URL detection</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should not detect non-Instagram URLs as Instagram</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">should not detect non-YouTube URLs as YouTube</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.12</a> at Jul 27, 2025, 11:34:50 PM</p>
</div>
</div>
</body>
</html>
