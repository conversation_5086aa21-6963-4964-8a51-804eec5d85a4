1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.downloader.app"
4    android:versionCode="2"
5    android:versionName="2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Internet permission for downloading files -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:6:22-64
13
14    <!-- Storage permissions for saving files -->
15    <uses-permission
15-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:12:9-35
21
22    <!-- For Android 13+ -->
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:15:5-76
23-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:15:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
24-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:16:5-75
24-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:16:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
25-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:17:5-75
25-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:17:22-72
26
27    <!-- Network state for checking connectivity -->
28    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
28-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:20:5-79
28-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:20:22-76
29
30    <!-- Foreground service for downloads -->
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:23:5-77
31-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:23:22-74
32    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
32-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:24:5-87
32-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:24:22-84
33
34    <!-- Notification permission for Android 13+ -->
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:27:5-77
35-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:27:22-74
36    <uses-permission android:name="android.permission.READ_CLIPBOARD" />
36-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:30:5-73
36-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:30:22-70
37    <uses-permission android:name="android.permission.WAKE_LOCK" />
37-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:5-68
37-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:22-65
38    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
38-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:5-81
38-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:22-78
39
40    <permission
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
41        android:name="com.downloader.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.downloader.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
45
46    <application
46-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:32:5-81:19
47        android:name="com.downloader.app.DawnApplication"
47-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:33:9-40
48        android:allowBackup="true"
48-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:34:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\74d3d7c2d24a7100d6b0d87b145b1bf3\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:35:9-65
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:fullBackupContent="@xml/backup_rules"
53-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:36:9-54
54        android:icon="@drawable/ic_dawn_logo"
54-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:37:9-46
55        android:label="@string/app_name"
55-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:38:9-41
56        android:networkSecurityConfig="@xml/network_security_config"
56-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:43:9-69
57        android:roundIcon="@drawable/ic_dawn_logo"
57-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:39:9-51
58        android:supportsRtl="true"
58-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:40:9-35
59        android:theme="@style/Theme.Downloader"
59-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:41:9-48
60        android:usesCleartextTraffic="true" >
60-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:42:9-44
61        <activity
61-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:46:9-61:20
62            android:name="com.downloader.app.MainActivity"
62-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:47:13-41
63            android:exported="true"
63-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:48:13-36
64            android:theme="@style/Theme.Downloader" >
64-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:49:13-52
65            <intent-filter>
65-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:50:13-53:29
66                <action android:name="android.intent.action.MAIN" />
66-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:51:17-69
66-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:51:25-66
67
68                <category android:name="android.intent.category.LAUNCHER" />
68-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:52:17-77
68-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:52:27-74
69            </intent-filter>
70
71            <!-- Handle shared URLs -->
72            <intent-filter>
72-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:56:13-60:29
73                <action android:name="android.intent.action.SEND" />
73-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:57:17-69
73-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:57:25-66
74
75                <category android:name="android.intent.category.DEFAULT" />
75-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:58:17-76
75-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:58:27-73
76
77                <data android:mimeType="text/plain" />
77-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:59:17-55
77-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:59:23-52
78            </intent-filter>
79        </activity>
80
81        <!-- Download Service -->
82        <service
82-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:64:9-68:56
83            android:name="com.downloader.app.service.DownloadService"
83-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:65:13-52
84            android:enabled="true"
84-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:66:13-35
85            android:exported="false"
85-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:67:13-37
86            android:foregroundServiceType="dataSync" />
86-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:68:13-53
87
88        <!-- File Provider for sharing files -->
89        <provider
90            android:name="androidx.core.content.FileProvider"
90-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:72:13-62
91            android:authorities="com.downloader.app.fileprovider"
91-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:73:13-64
92            android:exported="false"
92-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:74:13-37
93            android:grantUriPermissions="true" >
93-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:75:13-47
94            <meta-data
94-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:76:13-78:63
95                android:name="android.support.FILE_PROVIDER_PATHS"
95-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:77:17-67
96                android:resource="@xml/file_provider_paths" />
96-->C:\Users\<USER>\Desktop\rv\Downloader\app\src\main\AndroidManifest.xml:78:17-60
97        </provider>
98        <provider
98-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:29:9-37:20
99            android:name="androidx.startup.InitializationProvider"
99-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:30:13-67
100            android:authorities="com.downloader.app.androidx-startup"
100-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:31:13-68
101            android:exported="false" >
101-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:32:13-37
102            <meta-data
102-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:34:13-36:52
103                android:name="androidx.work.WorkManagerInitializer"
103-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:35:17-68
104                android:value="androidx.startup" />
104-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:36:17-49
105            <meta-data
105-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
106                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
106-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
107                android:value="androidx.startup" />
107-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\19d362a71b86adee42cba2548821e94b\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
108            <meta-data
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
109                android:name="androidx.emoji2.text.EmojiCompatInitializer"
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
110                android:value="androidx.startup" />
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\97fdf9696ef066c122f40b34cd98ae7b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
111            <meta-data
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
113                android:value="androidx.startup" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
114        </provider>
115
116        <service
116-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:39:9-45:35
117            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
117-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:40:13-88
118            android:directBootAware="false"
118-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:41:13-44
119            android:enabled="@bool/enable_system_alarm_service_default"
119-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:42:13-72
120            android:exported="false" />
120-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:43:13-37
121        <service
121-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:46:9-52:35
122            android:name="androidx.work.impl.background.systemjob.SystemJobService"
122-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:47:13-84
123            android:directBootAware="false"
123-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:48:13-44
124            android:enabled="@bool/enable_system_job_service_default"
124-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:49:13-70
125            android:exported="true"
125-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:50:13-36
126            android:permission="android.permission.BIND_JOB_SERVICE" />
126-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:51:13-69
127        <service
127-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:53:9-59:35
128            android:name="androidx.work.impl.foreground.SystemForegroundService"
128-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:54:13-81
129            android:directBootAware="false"
129-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:55:13-44
130            android:enabled="@bool/enable_system_foreground_service_default"
130-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:56:13-77
131            android:exported="false" />
131-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:57:13-37
132
133        <receiver
133-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:61:9-66:35
134            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
134-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:62:13-88
135            android:directBootAware="false"
135-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:63:13-44
136            android:enabled="true"
136-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:64:13-35
137            android:exported="false" />
137-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:65:13-37
138        <receiver
138-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:67:9-77:20
139            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
139-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:68:13-106
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:69:13-44
141            android:enabled="false"
141-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:70:13-36
142            android:exported="false" >
142-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:71:13-37
143            <intent-filter>
143-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:73:13-76:29
144                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
144-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:17-87
144-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:25-84
145                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
145-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:17-90
145-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:25-87
146            </intent-filter>
147        </receiver>
148        <receiver
148-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:78:9-88:20
149            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
149-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:79:13-104
150            android:directBootAware="false"
150-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:80:13-44
151            android:enabled="false"
151-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:81:13-36
152            android:exported="false" >
152-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:82:13-37
153            <intent-filter>
153-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:84:13-87:29
154                <action android:name="android.intent.action.BATTERY_OKAY" />
154-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:17-77
154-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:25-74
155                <action android:name="android.intent.action.BATTERY_LOW" />
155-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:17-76
155-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:25-73
156            </intent-filter>
157        </receiver>
158        <receiver
158-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:89:9-99:20
159            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
159-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:90:13-104
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:91:13-44
161            android:enabled="false"
161-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:92:13-36
162            android:exported="false" >
162-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:93:13-37
163            <intent-filter>
163-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:95:13-98:29
164                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
164-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:17-83
164-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:25-80
165                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
165-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:17-82
165-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:25-79
166            </intent-filter>
167        </receiver>
168        <receiver
168-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:100:9-109:20
169            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
169-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:101:13-103
170            android:directBootAware="false"
170-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:102:13-44
171            android:enabled="false"
171-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:103:13-36
172            android:exported="false" >
172-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:104:13-37
173            <intent-filter>
173-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:106:13-108:29
174                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
174-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:17-79
174-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:25-76
175            </intent-filter>
176        </receiver>
177        <receiver
177-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:110:9-121:20
178            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
178-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:111:13-88
179            android:directBootAware="false"
179-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:112:13-44
180            android:enabled="false"
180-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:113:13-36
181            android:exported="false" >
181-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:114:13-37
182            <intent-filter>
182-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:116:13-120:29
183                <action android:name="android.intent.action.BOOT_COMPLETED" />
183-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:117:17-79
183-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:117:25-76
184                <action android:name="android.intent.action.TIME_SET" />
184-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:17-73
184-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:25-70
185                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
185-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:17-81
185-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:25-78
186            </intent-filter>
187        </receiver>
188        <receiver
188-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:122:9-131:20
189            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
189-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:123:13-99
190            android:directBootAware="false"
190-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:124:13-44
191            android:enabled="@bool/enable_system_alarm_service_default"
191-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:125:13-72
192            android:exported="false" >
192-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:126:13-37
193            <intent-filter>
193-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:128:13-130:29
194                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
194-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:17-98
194-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:25-95
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:132:9-142:20
198            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
198-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:133:13-78
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:134:13-44
200            android:enabled="true"
200-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:135:13-35
201            android:exported="true"
201-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:136:13-36
202            android:permission="android.permission.DUMP" >
202-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:137:13-57
203            <intent-filter>
203-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:139:13-141:29
204                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
204-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:17-88
204-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\735e093a8b8e369710032a5f563fa25e\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:25-85
205            </intent-filter>
206        </receiver>
207
208        <activity
208-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
209            android:name="androidx.compose.ui.tooling.PreviewActivity"
209-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
210            android:exported="true" />
210-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d9b3f646291e9d11651583c5096c3e0\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
211        <activity
211-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
212            android:name="androidx.activity.ComponentActivity"
212-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
213            android:exported="true" />
213-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.12\transforms\593e0c25423b1a621fbe3d8d32854e3a\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
214
215        <service
215-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
216            android:name="androidx.room.MultiInstanceInvalidationService"
216-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
217            android:directBootAware="true"
217-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
218            android:exported="false" />
218-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\75d039dc8db082f9d946146df7b0e509\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
219
220        <receiver
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
221            android:name="androidx.profileinstaller.ProfileInstallReceiver"
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
222            android:directBootAware="false"
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
223            android:enabled="true"
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
224            android:exported="true"
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
225            android:permission="android.permission.DUMP" >
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
226            <intent-filter>
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
227                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
228            </intent-filter>
229            <intent-filter>
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
230                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
231            </intent-filter>
232            <intent-filter>
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
233                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
234            </intent-filter>
235            <intent-filter>
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
236                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\dedcede42a9a741173ca9e0fedf71fe2\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
237            </intent-filter>
238        </receiver>
239    </application>
240
241</manifest>
