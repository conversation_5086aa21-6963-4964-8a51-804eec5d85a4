import subprocess
import sys
import os

def download_video(url):
    try:
        # Define the output template. This will save the video with its title.
        # Ensure that the current working directory is used for saving files.
        output_template = os.path.join("%(title)s.%(ext)s")
        
        # Construct the command for yt-dlp
        # --output: specifies the output filename template
        # --quiet: suppress non-critical output
        # We'll also print stdout/stderr from subprocess for feedback.
        command = ["yt-dlp", "--output", output_template, url]

        print(f"Attempting to download from: {url}")
        print(f"Executing command: {' '.join(command)}")

        # Execute the command using subprocess.Popen for better control over output
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, cwd='c:/Users/<USER>/Desktop/rv/Downloader')
        stdout, stderr = process.communicate()

        if process.returncode == 0:
            print("Download successful!")
            # Print any relevant output from yt-dlp
            if stdout:
                print("yt-dlp output:")
                print(stdout)
        else:
            print(f"Download failed. Error code: {process.returncode}")
            if stderr:
                print("yt-dlp error output:")
                print(stderr)
            elif stdout: # Sometimes errors are printed to stdout by yt-dlp
                print("yt-dlp output (potentially error):")
                print(stdout)

    except FileNotFoundError:
        print("Error: 'yt-dlp' command not found. Please ensure yt-dlp is installed and accessible in your system's PATH.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        video_url = sys.argv[1]
        download_video(video_url)
    else:
        print("Usage: python downloader.py <video_url>")
        print("Example: python downloader.py https://www.youtube.com/watch?v=dQw4w9WgXcQ")
