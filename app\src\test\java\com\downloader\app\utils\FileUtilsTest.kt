package com.downloader.app.utils

import com.google.common.truth.Truth.assertThat
import org.junit.Test

class FileUtilsTest {

    @Test
    fun `getFileNameFromUrl should extract filename from valid URL`() {
        // Given
        val url = "https://example.com/path/to/file.mp4"
        
        // When
        val result = FileUtils.getFileNameFromUrl(url)
        
        // Then
        assertThat(result).isEqualTo("file.mp4")
    }

    @Test
    fun `getFileNameFromUrl should handle URL with query parameters`() {
        // Given
        val url = "https://example.com/video.mp4?quality=hd&format=mp4"
        
        // When
        val result = FileUtils.getFileNameFromUrl(url)
        
        // Then
        assertThat(result).isEqualTo("video.mp4")
    }

    @Test
    fun `getFileNameFromUrl should handle URL with fragment`() {
        // Given
        val url = "https://example.com/document.pdf#page=1"
        
        // When
        val result = FileUtils.getFileNameFromUrl(url)
        
        // Then
        assertThat(result).isEqualTo("document.pdf")
    }

    @Test
    fun `getFileNameFromUrl should generate default name for URL without filename`() {
        // Given
        val url = "https://example.com/"
        
        // When
        val result = FileUtils.getFileNameFromUrl(url)
        
        // Then
        assertThat(result).startsWith("download_")
    }

    @Test
    fun `getFileNameFromUrl should generate default name for URL without extension`() {
        // Given
        val url = "https://example.com/somepath"
        
        // When
        val result = FileUtils.getFileNameFromUrl(url)
        
        // Then
        assertThat(result).startsWith("download_")
    }

    @Test
    fun `getMimeTypeFromUrl should return a mime type for video`() {
        // Given
        val url = "https://example.com/video.mp4"

        // When
        val result = FileUtils.getMimeTypeFromUrl(url)

        // Then - Should return some mime type (Android MimeTypeMap not available in unit tests)
        assertThat(result).isNotEmpty()
    }

    @Test
    fun `getMimeTypeFromUrl should return a mime type for audio`() {
        // Given
        val url = "https://example.com/audio.mp3"

        // When
        val result = FileUtils.getMimeTypeFromUrl(url)

        // Then - Should return some mime type
        assertThat(result).isNotEmpty()
    }

    @Test
    fun `getMimeTypeFromUrl should return a mime type for image`() {
        // Given
        val url = "https://example.com/image.jpg"

        // When
        val result = FileUtils.getMimeTypeFromUrl(url)

        // Then - Should return some mime type
        assertThat(result).isNotEmpty()
    }

    @Test
    fun `getMimeTypeFromUrl should return default mime type for unknown extension`() {
        // Given
        val url = "https://example.com/file.unknown"
        
        // When
        val result = FileUtils.getMimeTypeFromUrl(url)
        
        // Then
        assertThat(result).isEqualTo("application/octet-stream")
    }

    @Test
    fun `isValidUrl should return true for valid HTTP URL`() {
        // Given
        val url = "http://example.com/file.mp4"
        
        // When
        val result = FileUtils.isValidUrl(url)
        
        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `isValidUrl should return true for valid HTTPS URL`() {
        // Given
        val url = "https://example.com/file.mp4"
        
        // When
        val result = FileUtils.isValidUrl(url)
        
        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `isValidUrl should return true for URL without protocol`() {
        // Given
        val url = "example.com/file.mp4"
        
        // When
        val result = FileUtils.isValidUrl(url)
        
        // Then
        assertThat(result).isTrue()
    }

    @Test
    fun `isValidUrl should return false for invalid URL`() {
        // Given
        val url = "not-a-url"
        
        // When
        val result = FileUtils.isValidUrl(url)
        
        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `isValidUrl should return false for empty string`() {
        // Given
        val url = ""
        
        // When
        val result = FileUtils.isValidUrl(url)
        
        // Then
        assertThat(result).isFalse()
    }

    @Test
    fun `sanitizeFileName should replace invalid characters`() {
        // Given
        val fileName = "file<>:\"/\\|?*.txt"
        
        // When
        val result = FileUtils.sanitizeFileName(fileName)
        
        // Then
        assertThat(result).isEqualTo("file_.txt")
    }

    @Test
    fun `sanitizeFileName should handle multiple consecutive underscores`() {
        // Given
        val fileName = "file___name.txt"
        
        // When
        val result = FileUtils.sanitizeFileName(fileName)
        
        // Then
        assertThat(result).isEqualTo("file_name.txt")
    }

    @Test
    fun `sanitizeFileName should trim leading and trailing underscores`() {
        // Given
        val fileName = "_file_name_.txt"
        
        // When
        val result = FileUtils.sanitizeFileName(fileName)
        
        // Then
        assertThat(result).isEqualTo("file_name_.txt")
    }
}
