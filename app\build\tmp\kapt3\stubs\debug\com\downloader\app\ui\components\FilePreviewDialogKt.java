package com.downloader.app.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0007\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\u0007\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0010\u0010\t\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0003\u001a\u0018\u0010\n\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\u000b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\r\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\b\u0010\u000e\u001a\u00020\u0001H\u0003\u001a(\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u0013H\u0007\u001a6\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u00182\u0006\u0010\u001a\u001a\u00020\u00182\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00010\u0011H\u0003\u001a\u0018\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0010\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020 H\u0003\u001a\u0018\u0010!\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\"\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010#\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a \u0010$\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010%\u001a\u00020\u0018H\u0002\u001a \u0010&\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010%\u001a\u00020\u0018H\u0002\u00a8\u0006'"}, d2 = {"APKPreview", "", "downloadItem", "Lcom/downloader/app/data/model/DownloadItem;", "context", "Landroid/content/Context;", "ArchivePreview", "AudioPreview", "CADPreview", "CodePreview", "DatabasePreview", "DocumentPreview", "EbookPreview", "ExecutablePreview", "FileNotFoundMessage", "FilePreviewDialog", "onDismiss", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "FileTypePreview", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "title", "", "fileName", "buttonText", "onOpen", "FontPreview", "GenericFilePreview", "ImagePreview", "file", "Ljava/io/File;", "PresentationPreview", "SpreadsheetPreview", "VideoPreview", "openFileExternally", "mimeType", "shareFile", "app_debug"})
public final class FilePreviewDialogKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void FilePreviewDialog(@org.jetbrains.annotations.NotNull()
    com.downloader.app.data.model.DownloadItem downloadItem, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ImagePreview(java.io.File file) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void VideoPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AudioPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CodePreview(com.downloader.app.data.model.DownloadItem downloadItem) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DocumentPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void GenericFilePreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FileNotFoundMessage() {
    }
    
    private static final void openFileExternally(android.content.Context context, java.io.File file, java.lang.String mimeType) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void PresentationPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SpreadsheetPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EbookPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ArchivePreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FontPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DatabasePreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CADPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ExecutablePreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void APKPreview(com.downloader.app.data.model.DownloadItem downloadItem, android.content.Context context) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void FileTypePreview(androidx.compose.ui.graphics.vector.ImageVector icon, java.lang.String title, java.lang.String fileName, java.lang.String buttonText, kotlin.jvm.functions.Function0<kotlin.Unit> onOpen) {
    }
    
    private static final void shareFile(android.content.Context context, java.io.File file, java.lang.String mimeType) {
    }
}