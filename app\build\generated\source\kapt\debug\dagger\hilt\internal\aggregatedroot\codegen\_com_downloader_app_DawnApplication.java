package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.downloader.app.DawnApplication",
    rootPackage = "com.downloader.app",
    originatingRoot = "com.downloader.app.DawnApplication",
    originatingRootPackage = "com.downloader.app",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "DawnApplication",
    originatingRootSimpleNames = "DawnApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_downloader_app_DawnApplication {
}
